'use client'

import React, { useState, useCallback, useMemo } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  ReactFlowProvider,
  Panel,
  BackgroundVariant,
  MiniMap,
  useReactFlow
} from 'reactflow'
import 'reactflow/dist/style.css'
import './pipeline.css'
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import { Database, Plus, Play, Save, Download, Code2 } from "lucide-react"
import { PipelineNode } from './PipelineNode'
import { SQLPreviewPanel } from './SQLPreviewPanel'
import { ResultTableView } from './ResultTableView'
import { DataPipelineProps, PipelineNodeData, Dataset } from './types'
import { generateSQL } from './sqlGenerator'
import { executePipeline as runPipeline } from './pipelineExecutor'
import { toast } from 'sonner'

const nodeTypes = {
  dataset: PipelineNode,
  join: PipelineNode,
  transform: PipelineNode,
  concat: PipelineNode,
  output: PipelineNode,
}

export function DataPipeline({ datasets, onSaveDataset }: DataPipelineProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [sqlPreview, setSqlPreview] = useState('')
  const [resultData, setResultData] = useState<any[]>([])
  const [resultHeaders, setResultHeaders] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)

  // Generate SQL preview when nodes or edges change
  const updateSQLPreview = useCallback(() => {
    try {
      const sql = generateSQL(nodes, edges)
      setSqlPreview(sql)
    } catch (error) {
      console.error('Error generating SQL:', error)
      setSqlPreview('-- Error generating SQL preview')
    }
  }, [nodes, edges])

  // Update SQL preview when nodes or edges change
  React.useEffect(() => {
    updateSQLPreview()
  }, [updateSQLPreview])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id)
  }, [])

  // Add dataset node to canvas
  const addDatasetNode = useCallback((dataset: Dataset) => {
    const newNode: Node<PipelineNodeData> = {
      id: `dataset-${dataset.id}`,
      type: 'dataset',
      position: { x: Math.random() * 300, y: Math.random() * 300 },
      data: {
        id: `dataset-${dataset.id}`,
        type: 'dataset',
        label: dataset.name,
        dataset,
        resultData: dataset.data,
        resultHeaders: dataset.headers
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added dataset: ${dataset.name}`)
  }, [setNodes])

  // Add operation node
  const addOperationNode = useCallback((type: 'join' | 'transform' | 'concat' | 'output') => {
    const newNode: Node<PipelineNodeData> = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 300 + 400, y: Math.random() * 300 },
      data: {
        id: `${type}-${Date.now()}`,
        type,
        label: type.charAt(0).toUpperCase() + type.slice(1),
        config: {}
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added ${type} node`)
  }, [setNodes])

  // Execute pipeline
  const executePipelineHandler = useCallback(async () => {
    setIsExecuting(true)
    try {
      const result = await runPipeline(nodes, edges)
      setResultData(result.data)
      setResultHeaders(result.headers)
      toast.success('Pipeline executed successfully')
    } catch (error) {
      console.error('Pipeline execution error:', error)
      toast.error('Failed to execute pipeline')
    } finally {
      setIsExecuting(false)
    }
  }, [nodes, edges])

  // Save result as new dataset
  const saveAsDataset = useCallback(async () => {
    if (resultData.length === 0) {
      toast.error('No data to save')
      return
    }

    const newDataset: Dataset = {
      id: `pipeline-result-${Date.now()}`,
      name: `Pipeline Result ${new Date().toLocaleString()}`,
      headers: resultHeaders,
      data: resultData,
      rowCount: resultData.length,
      fileType: 'pipeline',
      description: 'Generated from data pipeline'
    }

    if (onSaveDataset) {
      onSaveDataset(newDataset)
      toast.success('Dataset saved successfully')
    }
  }, [resultData, resultHeaders, onSaveDataset])

  return (
    <div className="h-full w-full flex flex-col">
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-4 border-b bg-background">
        <h2 className="text-lg font-semibold">Data Pipeline Builder</h2>
        <div className="flex-1" />
        
        {/* Dataset Selector */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Datasets:</span>
          {datasets.map((dataset) => (
            <Button
              key={dataset.id}
              variant="outline"
              size="sm"
              onClick={() => addDatasetNode(dataset)}
              className="text-xs"
            >
              <Database className="h-3 w-3 mr-1" />
              {dataset.name}
            </Button>
          ))}
        </div>

        {/* Operation Buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => addOperationNode('concat')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Concat
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addOperationNode('join')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Join
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addOperationNode('transform')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Transform
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addOperationNode('output')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Output
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="default"
            size="sm"
            onClick={executePipelineHandler}
            disabled={isExecuting || nodes.length === 0}
          >
            <Play className="h-3 w-3 mr-1" />
            {isExecuting ? 'Running...' : 'Run'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={saveAsDataset}
            disabled={resultData.length === 0}
          >
            <Save className="h-3 w-3 mr-1" />
            Save
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        {/* Workflow Canvas */}
        <ResizablePanel defaultSize={60} minSize={40}>
          <div className="h-full relative">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              fitView
              fitViewOptions={{ padding: 0.1 }}
              defaultEdgeOptions={{
                type: 'smoothstep',
                animated: true,
                style: { strokeWidth: 2 }
              }}
            >
              <Background variant={BackgroundVariant.Dots} />
              <Controls />
              <MiniMap />
              <Panel position="top-left" className="bg-background/95 p-2 rounded border">
                <div className="text-xs text-muted-foreground">
                  Drag datasets and connect nodes to build your pipeline
                </div>
              </Panel>
            </ReactFlow>
          </div>
        </ResizablePanel>

        <ResizableHandle />

        {/* Right Panel */}
        <ResizablePanel defaultSize={40} minSize={30}>
          <ResizablePanelGroup direction="vertical">
            {/* SQL Preview */}
            <ResizablePanel defaultSize={30} minSize={20}>
              <SQLPreviewPanel sql={sqlPreview} />
            </ResizablePanel>

            <ResizableHandle />

            {/* Result Table */}
            <ResizablePanel defaultSize={70} minSize={40}>
              <ResultTableView
                data={resultData}
                headers={resultHeaders}
                onExport={() => {
                  // TODO: Implement CSV export
                  toast.info('Export functionality coming soon')
                }}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}

// Wrap with ReactFlowProvider
export function DataPipelineWrapper(props: DataPipelineProps) {
  return (
    <ReactFlowProvider>
      <DataPipeline {...props} />
    </ReactFlowProvider>
  )
}
