'use client'

import React, { useState, useCallback, useEffect } from 'react'
import React<PERSON>low, {
  Node,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  ReactFlowProvider,
  BackgroundVariant,
  MiniMap
} from 'reactflow'
import 'reactflow/dist/style.css'
import './pipeline.css'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import {
  Database,
  Plus,
  Play,
  Save,
  Download,
  Code2,
  GitMerge,
  Filter,
  Layers,
  Target,
  Workflow,
  Table2,
  <PERSON>ting<PERSON>,
  Trash2,
  X
} from "lucide-react"
import { PipelineNode } from './PipelineNode'
import { SQLPreviewPanel } from './SQLPreviewPanel'
import { ResultTableView } from './ResultTableView'
import { NodeConfigPanel } from './NodeConfigPanel'
import { DataPipelineProps, PipelineNodeData, Dataset } from './types'
import { generateSQL } from './sqlGenerator'
import { executePipeline as runPipeline } from './pipelineExecutor'
import { toast } from 'sonner'


const nodeTypes = {
  dataset: PipelineNode,
  join: PipelineNode,
  transform: PipelineNode,
  concat: PipelineNode,
  output: PipelineNode,
}

export function DataPipeline({ datasets, onSaveDataset }: DataPipelineProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [selectedDataset, setSelectedDataset] = useState<string>('')
  const [sqlPreview, setSqlPreview] = useState('')
  const [resultData, setResultData] = useState<any[]>([])
  const [resultHeaders, setResultHeaders] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)
  const [showSQL, setShowSQL] = useState(true)
  const [showResults, setShowResults] = useState(true)
  const [showConfigPanel, setShowConfigPanel] = useState(false)
  const [configNodeId, setConfigNodeId] = useState<string | null>(null)

  // Debug effect to check dataset data
  useEffect(() => {
    console.log('DataPipeline: Received datasets:', datasets.map(d => ({
      id: d.id,
      name: d.name,
      rowCount: d.rowCount,
      dataLength: d.data?.length,
      hasData: !!d.data,
      sampleData: d.data?.slice(0, 2)
    })))
  }, [datasets])

  // Generate SQL preview when nodes or edges change
  const updateSQLPreview = useCallback(() => {
    try {
      const sql = generateSQL(nodes, edges)
      setSqlPreview(sql)
    } catch (error) {
      console.error('Error generating SQL:', error)
      setSqlPreview('-- Error generating SQL preview')
    }
  }, [nodes, edges])

  // Update SQL preview when nodes or edges change
  React.useEffect(() => {
    updateSQLPreview()
  }, [updateSQLPreview])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id)

    // Show config panel for configurable nodes
    if (node.data.type !== 'dataset' && node.data.type !== 'output') {
      setConfigNodeId(node.id)
      setShowConfigPanel(true)
    }
  }, [])

  // Add dataset node to canvas
  const addDatasetNode = useCallback((datasetId?: string) => {
    const dataset = datasets.find(d => d.id === (datasetId || selectedDataset))
    if (!dataset) {
      toast.error('Please select a dataset first')
      return
    }

    // Check if dataset already exists in the canvas
    const existingNode = nodes.find(node => node.id === `dataset-${dataset.id}`)
    if (existingNode) {
      toast.warning('Dataset already added to canvas')
      return
    }

    const newNode: Node<PipelineNodeData> = {
      id: `dataset-${dataset.id}`,
      type: 'dataset',
      position: {
        x: 50 + nodes.filter(n => n.data.type === 'dataset').length * 250,
        y: 100
      },
      data: {
        id: `dataset-${dataset.id}`,
        type: 'dataset',
        label: dataset.name,
        dataset,
        resultData: dataset.data,
        resultHeaders: dataset.headers
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added dataset: ${dataset.name}`)
  }, [datasets, selectedDataset, nodes, setNodes])

  // Add operation node
  const addOperationNode = useCallback((type: 'join' | 'transform' | 'concat' | 'output') => {
    const newNode: Node<PipelineNodeData> = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 300 + 400, y: Math.random() * 300 },
      data: {
        id: `${type}-${Date.now()}`,
        type,
        label: type.charAt(0).toUpperCase() + type.slice(1),
        config: {}
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added ${type} node`)
  }, [setNodes])

  // Execute pipeline
  const executePipelineHandler = useCallback(async () => {
    setIsExecuting(true)
    try {
      const result = await runPipeline(nodes, edges)
      setResultData(result.data)
      setResultHeaders(result.headers)
      toast.success('Pipeline executed successfully')
    } catch (error) {
      console.error('Pipeline execution error:', error)
      toast.error('Failed to execute pipeline')
    } finally {
      setIsExecuting(false)
    }
  }, [nodes, edges])

  // Save result as new dataset
  const saveAsDataset = useCallback(async () => {
    if (resultData.length === 0) {
      toast.error('No data to save')
      return
    }

    const newDataset: Dataset = {
      id: `pipeline-result-${Date.now()}`,
      name: `Pipeline Result ${new Date().toLocaleString()}`,
      headers: resultHeaders,
      data: resultData,
      rowCount: resultData.length,
      fileType: 'pipeline',
      description: 'Generated from data pipeline'
    }

    if (onSaveDataset) {
      onSaveDataset(newDataset)
      toast.success('Dataset saved successfully')
    }
  }, [resultData, resultHeaders, onSaveDataset])

  // Handle node configuration changes
  const handleNodeConfigChange = useCallback((nodeId: string, config: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                config,
                // Update label for transform nodes with column selection
                label: node.data.type === 'transform' && config.transformType === 'select' && config.selectedColumns
                  ? `Select (${config.selectedColumns.length} cols)`
                  : node.data.label
              }
            }
          : node
      )
    )
  }, [setNodes])

  return (
    <div className="h-full w-full flex flex-col overflow-hidden"
         style={{ maxHeight: 'calc(100vh - 120px)' }}>
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-card">
        <div className="flex items-center justify-between p-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Workflow className="h-4 w-4 text-primary" />
              <h1 className="text-lg font-semibold">Data Pipeline Builder</h1>
            </div>
            <Badge variant="secondary" className="text-xs">
              {nodes.length} nodes • {edges.length} connections
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={executePipelineHandler}
              disabled={isExecuting || nodes.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              <Play className="h-3 w-3 mr-1" />
              {isExecuting ? 'Running...' : 'Run'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveAsDataset}
              disabled={resultData.length === 0}
            >
              <Save className="h-3 w-3 mr-1" />
              Save
            </Button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex-shrink-0 flex items-center gap-3 px-3 pb-3 overflow-x-auto">
          {/* Dataset Selector */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <Database className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs font-medium">Dataset:</span>
            <Select value={selectedDataset} onValueChange={setSelectedDataset}>
              <SelectTrigger className="w-40 h-7">
                <SelectValue placeholder="Select..." />
              </SelectTrigger>
              <SelectContent>
                {datasets.map((dataset) => (
                  <SelectItem key={dataset.id} value={dataset.id}>
                    <div className="flex items-center gap-2">
                      <Database className="h-3 w-3" />
                      <span className="truncate">{dataset.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {dataset.data?.length || dataset.rowCount || 0}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addDatasetNode()}
              disabled={!selectedDataset}
              className="h-7 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          </div>

          <Separator orientation="vertical" className="h-4" />

          {/* Operations Dropdown */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-xs font-medium">Operations:</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                  <Plus className="h-3 w-3 mr-1" />
                  Add Node
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-44">
                <DropdownMenuItem onClick={() => addOperationNode('concat')}>
                  <Layers className="h-3 w-3 mr-2" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Concat</span>
                    <span className="text-xs text-muted-foreground">Combine datasets</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('join')}>
                  <GitMerge className="h-3 w-3 mr-2" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Join</span>
                    <span className="text-xs text-muted-foreground">Merge on keys</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('transform')}>
                  <Filter className="h-3 w-3 mr-2" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Transform</span>
                    <span className="text-xs text-muted-foreground">Filter & modify</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('output')}>
                  <Target className="h-3 w-3 mr-2" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Output</span>
                    <span className="text-xs text-muted-foreground">Final result</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Separator orientation="vertical" className="h-4" />

          {/* View Controls */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-xs font-medium">Panels:</span>
            <Button
              variant={showSQL ? "default" : "outline"}
              size="sm"
              onClick={() => setShowSQL(!showSQL)}
              className="h-7 px-2 text-xs"
            >
              <Code2 className="h-3 w-3 mr-1" />
              SQL
            </Button>
            <Button
              variant={showResults ? "default" : "outline"}
              size="sm"
              onClick={() => setShowResults(!showResults)}
              className="h-7 px-2 text-xs"
            >
              <Table2 className="h-3 w-3 mr-1" />
              Results
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-hidden" style={{ height: 'calc(100% - 120px)' }}>
        <ResizablePanelGroup direction="horizontal" className="h-full w-full">
          {/* Workflow Canvas */}
          <ResizablePanel defaultSize={60} minSize={40} className="h-full">
            <div className="h-full flex flex-col">
              {/* Canvas Header */}
              <div className="flex-shrink-0 flex items-center justify-between p-2 border-b bg-muted/30">
                <div className="flex items-center gap-2">
                  <Workflow className="h-4 w-4" />
                  <span className="text-sm font-medium">Pipeline Workflow</span>
                </div>
                <div className="flex items-center gap-2">
                  {selectedNode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNodes(nodes.filter(n => n.id !== selectedNode))
                        setSelectedNode(null)
                      }}
                      className="h-7 px-2 text-xs"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  )}
                  <Badge variant="outline" className="text-xs">
                    {nodes.length === 0 ? 'Empty' : `${nodes.length} nodes`}
                  </Badge>
                </div>
              </div>

              {/* Canvas Content */}
              <div className="flex-1 min-h-0">
                {nodes.length === 0 ? (
                  <div className="h-full flex items-center justify-center text-center">
                    <div className="space-y-3">
                      <div className="w-12 h-12 mx-auto bg-muted rounded-full flex items-center justify-center">
                        <Workflow className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium">Build Your Data Pipeline</h3>
                        <p className="text-xs text-muted-foreground mt-1">
                          Select a dataset and add it to the canvas to get started
                        </p>
                      </div>
                      <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
                        <span>1. Add Dataset</span>
                        <span>→</span>
                        <span>2. Add Operations</span>
                        <span>→</span>
                        <span>3. Connect</span>
                        <span>→</span>
                        <span>4. Run</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <ReactFlow
                    nodes={nodes}
                    edges={edges}
                    onNodesChange={onNodesChange}
                    onEdgesChange={onEdgesChange}
                    onConnect={onConnect}
                    onNodeClick={onNodeClick}
                    nodeTypes={nodeTypes}
                    fitView
                    fitViewOptions={{ padding: 0.1 }}
                    defaultEdgeOptions={{
                      type: 'smoothstep',
                      animated: true,
                      style: { strokeWidth: 2, stroke: '#6366f1' }
                    }}
                    className="w-full h-full"
                  >
                    <Background
                      variant={BackgroundVariant.Dots}
                      gap={20}
                      size={1}
                      color="#e2e8f0"
                    />
                    <Controls
                      className="bg-background border border-border rounded-lg shadow-sm"
                    />
                    <MiniMap
                      className="bg-background border border-border rounded-lg shadow-sm"
                      nodeColor="#6366f1"
                      maskColor="rgba(0, 0, 0, 0.1)"
                    />
                  </ReactFlow>
                )}
              </div>
            </div>
          </ResizablePanel>

        <ResizableHandle />

        {/* Right Panels */}
        <ResizablePanel defaultSize={40} minSize={25} maxSize={60} className="h-full">
          {!showSQL && !showResults ? (
            // Show panel selector when both panels are hidden
            <div className="h-full flex items-center justify-center border-l">
              <div className="text-center space-y-3">
                <div className="text-sm text-muted-foreground">
                  Select panels to view
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSQL(true)}
                    className="text-xs"
                  >
                    <Code2 className="h-3 w-3 mr-1" />
                    Show SQL
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowResults(true)}
                    className="text-xs"
                  >
                    <Table2 className="h-3 w-3 mr-1" />
                    Show Results
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <ResizablePanelGroup direction="vertical" className="h-full">
              {/* SQL Preview Panel */}
              {showSQL && (
                <>
                  <ResizablePanel defaultSize={40} minSize={20} className="h-full">
                    <div className="h-full flex flex-col border-l">
                      <div className="flex-shrink-0 flex items-center justify-between p-2 border-b bg-muted/30">
                        <div className="flex items-center gap-2">
                          <Code2 className="h-4 w-4" />
                          <span className="text-sm font-medium">Generated SQL</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge variant="secondary" className="text-xs">
                            Live
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowSQL(false)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex-1 min-h-0">
                        <SQLPreviewPanel sql={sqlPreview} />
                      </div>
                    </div>
                  </ResizablePanel>

                  {showResults && <ResizableHandle />}
                </>
              )}

              {/* Results Panel */}
              {showResults && (
                <ResizablePanel defaultSize={showSQL ? 60 : 100} minSize={30} className="h-full">
                  <div className="h-full flex flex-col border-l">
                    <div className="flex-shrink-0 flex items-center justify-between p-2 border-b bg-muted/30">
                      <div className="flex items-center gap-2">
                        <Table2 className="h-4 w-4" />
                        <span className="text-sm font-medium">Pipeline Results</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {resultData.length > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {resultData.length} rows
                          </Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowResults(false)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex-1 min-h-0">
                      <ResultTableView
                        data={resultData}
                        headers={resultHeaders}
                        onExport={() => {
                          // TODO: Implement CSV export
                          toast.info('Export functionality coming soon')
                        }}
                      />
                    </div>
                  </div>
                </ResizablePanel>
              )}
            </ResizablePanelGroup>
          )}
        </ResizablePanel>
      </ResizablePanelGroup>
      </div>

      {/* Configuration Panel */}
      {showConfigPanel && configNodeId && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <NodeConfigPanel
            node={nodes.find(n => n.id === configNodeId)!}
            availableDatasets={datasets}
            allNodes={nodes}
            edges={edges}
            onConfigChange={handleNodeConfigChange}
            onClose={() => {
              setShowConfigPanel(false)
              setConfigNodeId(null)
            }}
          />
        </div>
      )}
    </div>
  )
}

// Wrap with ReactFlowProvider
export function DataPipelineWrapper(props: DataPipelineProps) {
  return (
    <div className="h-full w-full overflow-hidden" style={{ height: '100%', maxHeight: '100%' }}>
      <ReactFlowProvider>
        <DataPipeline {...props} />
      </ReactFlowProvider>
    </div>
  )
}