'use client'

import React, { useState, useCallback, useEffect } from 'react'
import React<PERSON>low, {
  Node,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  ReactFlowProvider,
  BackgroundVariant,
  MiniMap
} from 'reactflow'
import 'reactflow/dist/style.css'
import './pipeline.css'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import {
  Database,
  Plus,
  Play,
  Save,
  Download,
  Code2,
  GitMerge,
  Filter,
  Layers,
  Target,
  Workflow,
  Table2,
  <PERSON>ting<PERSON>,
  Trash2,
  X
} from "lucide-react"
import { PipelineNode } from './PipelineNode'
import { SQLPreviewPanel } from './SQLPreviewPanel'
import { ResultTableView } from './ResultTableView'
import { NodeConfigPanel } from './NodeConfigPanel'
import { DataPipelineProps, PipelineNodeData, Dataset } from './types'
import { generateSQL } from './sqlGenerator'
import { executePipeline as runPipeline } from './pipelineExecutor'
import { toast } from 'sonner'


const nodeTypes = {
  dataset: PipelineNode,
  join: PipelineNode,
  transform: PipelineNode,
  concat: PipelineNode,
  output: PipelineNode,
}

export function DataPipeline({ datasets, onSaveDataset }: DataPipelineProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [selectedDataset, setSelectedDataset] = useState<string>('')
  const [sqlPreview, setSqlPreview] = useState('')
  const [resultData, setResultData] = useState<any[]>([])
  const [resultHeaders, setResultHeaders] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)
  const [showSQL, setShowSQL] = useState(true)
  const [showResults, setShowResults] = useState(true)
  const [showConfigPanel, setShowConfigPanel] = useState(false)
  const [configNodeId, setConfigNodeId] = useState<string | null>(null)

  // Debug effect to check dataset data
  useEffect(() => {
    console.log('DataPipeline: Received datasets:', datasets.map(d => ({
      id: d.id,
      name: d.name,
      rowCount: d.rowCount,
      dataLength: d.data?.length,
      hasData: !!d.data,
      sampleData: d.data?.slice(0, 2)
    })))
  }, [datasets])

  // Generate SQL preview when nodes or edges change
  const updateSQLPreview = useCallback(() => {
    try {
      const sql = generateSQL(nodes, edges)
      setSqlPreview(sql)
    } catch (error) {
      console.error('Error generating SQL:', error)
      setSqlPreview('-- Error generating SQL preview')
    }
  }, [nodes, edges])

  // Update SQL preview when nodes or edges change
  React.useEffect(() => {
    updateSQLPreview()
  }, [updateSQLPreview])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id)

    // Show config panel for configurable nodes
    if (node.data.type !== 'dataset' && node.data.type !== 'output') {
      setConfigNodeId(node.id)
      setShowConfigPanel(true)
    }
  }, [])

  // Add dataset node to canvas
  const addDatasetNode = useCallback((datasetId?: string) => {
    const dataset = datasets.find(d => d.id === (datasetId || selectedDataset))
    if (!dataset) {
      toast.error('Please select a dataset first')
      return
    }

    // Check if dataset already exists in the canvas
    const existingNode = nodes.find(node => node.id === `dataset-${dataset.id}`)
    if (existingNode) {
      toast.warning('Dataset already added to canvas')
      return
    }

    const newNode: Node<PipelineNodeData> = {
      id: `dataset-${dataset.id}`,
      type: 'dataset',
      position: {
        x: 50 + nodes.filter(n => n.data.type === 'dataset').length * 250,
        y: 100
      },
      data: {
        id: `dataset-${dataset.id}`,
        type: 'dataset',
        label: dataset.name,
        dataset,
        resultData: dataset.data,
        resultHeaders: dataset.headers
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added dataset: ${dataset.name}`)
  }, [datasets, selectedDataset, nodes, setNodes])

  // Add operation node
  const addOperationNode = useCallback((type: 'join' | 'transform' | 'concat' | 'output') => {
    const newNode: Node<PipelineNodeData> = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 300 + 400, y: Math.random() * 300 },
      data: {
        id: `${type}-${Date.now()}`,
        type,
        label: type.charAt(0).toUpperCase() + type.slice(1),
        config: {}
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added ${type} node`)
  }, [setNodes])

  // Execute pipeline
  const executePipelineHandler = useCallback(async () => {
    setIsExecuting(true)
    try {
      const result = await runPipeline(nodes, edges)
      setResultData(result.data)
      setResultHeaders(result.headers)
      toast.success('Pipeline executed successfully')
    } catch (error) {
      console.error('Pipeline execution error:', error)
      toast.error('Failed to execute pipeline')
    } finally {
      setIsExecuting(false)
    }
  }, [nodes, edges])

  // Save result as new dataset
  const saveAsDataset = useCallback(async () => {
    if (resultData.length === 0) {
      toast.error('No data to save')
      return
    }

    const newDataset: Dataset = {
      id: `pipeline-result-${Date.now()}`,
      name: `Pipeline Result ${new Date().toLocaleString()}`,
      headers: resultHeaders,
      data: resultData,
      rowCount: resultData.length,
      fileType: 'pipeline',
      description: 'Generated from data pipeline'
    }

    if (onSaveDataset) {
      onSaveDataset(newDataset)
      toast.success('Dataset saved successfully')
    }
  }, [resultData, resultHeaders, onSaveDataset])

  // Handle node configuration changes
  const handleNodeConfigChange = useCallback((nodeId: string, config: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                config,
                // Update label for transform nodes with column selection
                label: node.data.type === 'transform' && config.transformType === 'select' && config.selectedColumns
                  ? `Select (${config.selectedColumns.length} cols)`
                  : node.data.label
              }
            }
          : node
      )
    )
  }, [setNodes])

  return (
    <div className="h-full w-full flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Workflow className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-semibold">Data Pipeline Builder</h1>
            </div>
            <Badge variant="secondary" className="text-xs">
              {nodes.length} nodes • {edges.length} connections
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={executePipelineHandler}
              disabled={isExecuting || nodes.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              <Play className="h-4 w-4 mr-2" />
              {isExecuting ? 'Running Pipeline...' : 'Run Pipeline'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveAsDataset}
              disabled={resultData.length === 0}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Result
            </Button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex items-center gap-4 px-4 pb-4">
          {/* Dataset Selector */}
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Add Dataset:</span>
            <Select value={selectedDataset} onValueChange={setSelectedDataset}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select a dataset..." />
              </SelectTrigger>
              <SelectContent>
                {datasets.map((dataset) => (
                  <SelectItem key={dataset.id} value={dataset.id}>
                    <div className="flex items-center gap-2">
                      <Database className="h-3 w-3" />
                      <span>{dataset.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {dataset.data?.length || dataset.rowCount || 0} rows
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addDatasetNode()}
              disabled={!selectedDataset}
            >
              <Plus className="h-3 w-3 mr-1" />
              Add to Canvas
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Operations Dropdown */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Add Operation:</span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="text-xs">
                  <Plus className="h-3 w-3 mr-1" />
                  Operations
                  <Settings className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48">
                <DropdownMenuItem onClick={() => addOperationNode('concat')}>
                  <Layers className="h-4 w-4 mr-2" />
                  <div className="flex flex-col">
                    <span className="font-medium">Concat</span>
                    <span className="text-xs text-muted-foreground">Combine datasets</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('join')}>
                  <GitMerge className="h-4 w-4 mr-2" />
                  <div className="flex flex-col">
                    <span className="font-medium">Join</span>
                    <span className="text-xs text-muted-foreground">Merge on keys</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('transform')}>
                  <Filter className="h-4 w-4 mr-2" />
                  <div className="flex flex-col">
                    <span className="font-medium">Transform</span>
                    <span className="text-xs text-muted-foreground">Filter & modify</span>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => addOperationNode('output')}>
                  <Target className="h-4 w-4 mr-2" />
                  <div className="flex flex-col">
                    <span className="font-medium">Output</span>
                    <span className="text-xs text-muted-foreground">Final result</span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* View Controls */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Panels:</span>
            <Button
              variant={showSQL ? "default" : "outline"}
              size="sm"
              onClick={() => setShowSQL(!showSQL)}
              className="text-xs"
            >
              <Code2 className="h-3 w-3 mr-1" />
              SQL {showSQL ? '✓' : ''}
            </Button>
            <Button
              variant={showResults ? "default" : "outline"}
              size="sm"
              onClick={() => setShowResults(!showResults)}
              className="text-xs"
            >
              <Table2 className="h-3 w-3 mr-1" />
              Results {showResults ? '✓' : ''}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        {/* Workflow Canvas */}
        <ResizablePanel defaultSize={60} minSize={40}>
          <Card className="h-full rounded-none border-0">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <Workflow className="h-4 w-4" />
                  Pipeline Workflow
                </CardTitle>
                <div className="flex items-center gap-2">
                  {selectedNode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNodes(nodes.filter(n => n.id !== selectedNode))
                        setSelectedNode(null)
                      }}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete Node
                    </Button>
                  )}
                  <Badge variant="outline" className="text-xs">
                    {nodes.length === 0 ? 'Empty Canvas' : `${nodes.length} nodes`}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-4rem)]">
              {nodes.length === 0 ? (
                <div className="h-full flex items-center justify-center text-center">
                  <div className="space-y-4">
                    <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                      <Workflow className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Build Your Data Pipeline</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Select a dataset from the dropdown above and add it to the canvas to get started
                      </p>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                      <span>1. Add Dataset</span>
                      <span>→</span>
                      <span>2. Add Operations</span>
                      <span>→</span>
                      <span>3. Connect Nodes</span>
                      <span>→</span>
                      <span>4. Run Pipeline</span>
                    </div>
                  </div>
                </div>
              ) : (
                <ReactFlow
                  nodes={nodes}
                  edges={edges}
                  onNodesChange={onNodesChange}
                  onEdgesChange={onEdgesChange}
                  onConnect={onConnect}
                  onNodeClick={onNodeClick}
                  nodeTypes={nodeTypes}
                  fitView
                  fitViewOptions={{ padding: 0.1 }}
                  defaultEdgeOptions={{
                    type: 'smoothstep',
                    animated: true,
                    style: { strokeWidth: 2, stroke: '#6366f1' }
                  }}
                  className="bg-muted/20"
                >
                  <Background
                    variant={BackgroundVariant.Dots}
                    gap={20}
                    size={1}
                    color="#e2e8f0"
                  />
                  <Controls
                    className="bg-background border border-border rounded-lg shadow-sm"
                  />
                  <MiniMap
                    className="bg-background border border-border rounded-lg shadow-sm"
                    nodeColor="#6366f1"
                    maskColor="rgba(0, 0, 0, 0.1)"
                  />
                </ReactFlow>
              )}
            </CardContent>
          </Card>
        </ResizablePanel>

        <ResizableHandle />

        {/* Right Panels */}
        <ResizablePanel defaultSize={40} minSize={25} maxSize={60}>
          {!showSQL && !showResults ? (
            // Show panel selector when both panels are hidden
            <Card className="h-full rounded-none border-0">
              <CardContent className="h-full flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Select panels to view
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowSQL(true)}
                      className="text-xs"
                    >
                      <Code2 className="h-3 w-3 mr-1" />
                      Show SQL
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowResults(true)}
                      className="text-xs"
                    >
                      <Table2 className="h-3 w-3 mr-1" />
                      Show Results
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <ResizablePanelGroup direction="vertical">
              {/* SQL Preview Panel */}
              {showSQL && (
                <>
                  <ResizablePanel defaultSize={40} minSize={20}>
                    <Card className="h-full rounded-none border-0">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <Code2 className="h-4 w-4" />
                            Generated SQL
                          </CardTitle>
                          <div className="flex items-center gap-1">
                            <Badge variant="secondary" className="text-xs">
                              Live
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowSQL(false)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-0 h-[calc(100%-3rem)]">
                        <SQLPreviewPanel sql={sqlPreview} />
                      </CardContent>
                    </Card>
                  </ResizablePanel>

                  {showResults && <ResizableHandle />}
                </>
              )}

              {/* Results Panel */}
              {showResults && (
                <ResizablePanel defaultSize={showSQL ? 60 : 100} minSize={30}>
                  <Card className="h-full rounded-none border-0">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Table2 className="h-4 w-4" />
                          Pipeline Results
                        </CardTitle>
                        <div className="flex items-center gap-1">
                          {resultData.length > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {resultData.length} rows
                            </Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowResults(false)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0 h-[calc(100%-3rem)]">
                      <ResultTableView
                        data={resultData}
                        headers={resultHeaders}
                        onExport={() => {
                          // TODO: Implement CSV export
                          toast.info('Export functionality coming soon')
                        }}
                      />
                    </CardContent>
                  </Card>
                </ResizablePanel>
              )}
            </ResizablePanelGroup>
          )}
        </ResizablePanel>
      </ResizablePanelGroup>

      {/* Configuration Panel */}
      {showConfigPanel && configNodeId && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <NodeConfigPanel
            node={nodes.find(n => n.id === configNodeId)!}
            availableDatasets={datasets}
            allNodes={nodes}
            edges={edges}
            onConfigChange={handleNodeConfigChange}
            onClose={() => {
              setShowConfigPanel(false)
              setConfigNodeId(null)
            }}
          />
        </div>
      )}
    </div>
  )
}

// Wrap with ReactFlowProvider
export function DataPipelineWrapper(props: DataPipelineProps) {
  return (
    <ReactFlowProvider>
      <DataPipeline {...props} />
    </ReactFlowProvider>
  )
}