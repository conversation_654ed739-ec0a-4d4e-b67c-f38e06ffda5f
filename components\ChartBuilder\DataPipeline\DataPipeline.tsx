'use client'

import React, { useState, useCallback, useMemo } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  ReactFlowProvider,
  Panel,
  BackgroundVariant,
  MiniMap,
  useReactFlow
} from 'reactflow'
import 'reactflow/dist/style.css'
import './pipeline.css'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Database,
  Plus,
  Play,
  Save,
  Download,
  Code2,
  GitMerge,
  Filter,
  Layers,
  Target,
  Workflow,
  Table2,
  Settings,
  Trash2
} from "lucide-react"
import { PipelineNode } from './PipelineNode'
import { SQLPreviewPanel } from './SQLPreviewPanel'
import { ResultTableView } from './ResultTableView'
import { DataPipelineProps, PipelineNodeData, Dataset } from './types'
import { generateSQL } from './sqlGenerator'
import { executePipeline as runPipeline } from './pipelineExecutor'
import { toast } from 'sonner'


const nodeTypes = {
  dataset: PipelineNode,
  join: PipelineNode,
  transform: PipelineNode,
  concat: PipelineNode,
  output: PipelineNode,
}

export function DataPipeline({ datasets, onSaveDataset }: DataPipelineProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [selectedDataset, setSelectedDataset] = useState<string>('')
  const [sqlPreview, setSqlPreview] = useState('')
  const [resultData, setResultData] = useState<any[]>([])
  const [resultHeaders, setResultHeaders] = useState<string[]>([])
  const [isExecuting, setIsExecuting] = useState(false)
  const [showSQL, setShowSQL] = useState(true)
  const [showResults, setShowResults] = useState(true)

  // Generate SQL preview when nodes or edges change
  const updateSQLPreview = useCallback(() => {
    try {
      const sql = generateSQL(nodes, edges)
      setSqlPreview(sql)
    } catch (error) {
      console.error('Error generating SQL:', error)
      setSqlPreview('-- Error generating SQL preview')
    }
  }, [nodes, edges])

  // Update SQL preview when nodes or edges change
  React.useEffect(() => {
    updateSQLPreview()
  }, [updateSQLPreview])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id)
  }, [])

  // Add dataset node to canvas
  const addDatasetNode = useCallback((datasetId?: string) => {
    const dataset = datasets.find(d => d.id === (datasetId || selectedDataset))
    if (!dataset) {
      toast.error('Please select a dataset first')
      return
    }

    // Check if dataset already exists in the canvas
    const existingNode = nodes.find(node => node.id === `dataset-${dataset.id}`)
    if (existingNode) {
      toast.warning('Dataset already added to canvas')
      return
    }

    const newNode: Node<PipelineNodeData> = {
      id: `dataset-${dataset.id}`,
      type: 'dataset',
      position: {
        x: 50 + nodes.filter(n => n.data.type === 'dataset').length * 250,
        y: 100
      },
      data: {
        id: `dataset-${dataset.id}`,
        type: 'dataset',
        label: dataset.name,
        dataset,
        resultData: dataset.data,
        resultHeaders: dataset.headers
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added dataset: ${dataset.name}`)
  }, [datasets, selectedDataset, nodes, setNodes])

  // Add operation node
  const addOperationNode = useCallback((type: 'join' | 'transform' | 'concat' | 'output') => {
    const newNode: Node<PipelineNodeData> = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: Math.random() * 300 + 400, y: Math.random() * 300 },
      data: {
        id: `${type}-${Date.now()}`,
        type,
        label: type.charAt(0).toUpperCase() + type.slice(1),
        config: {}
      }
    }
    setNodes((nds) => [...nds, newNode])
    toast.success(`Added ${type} node`)
  }, [setNodes])

  // Execute pipeline
  const executePipelineHandler = useCallback(async () => {
    setIsExecuting(true)
    try {
      const result = await runPipeline(nodes, edges)
      setResultData(result.data)
      setResultHeaders(result.headers)
      toast.success('Pipeline executed successfully')
    } catch (error) {
      console.error('Pipeline execution error:', error)
      toast.error('Failed to execute pipeline')
    } finally {
      setIsExecuting(false)
    }
  }, [nodes, edges])

  // Save result as new dataset
  const saveAsDataset = useCallback(async () => {
    if (resultData.length === 0) {
      toast.error('No data to save')
      return
    }

    const newDataset: Dataset = {
      id: `pipeline-result-${Date.now()}`,
      name: `Pipeline Result ${new Date().toLocaleString()}`,
      headers: resultHeaders,
      data: resultData,
      rowCount: resultData.length,
      fileType: 'pipeline',
      description: 'Generated from data pipeline'
    }

    if (onSaveDataset) {
      onSaveDataset(newDataset)
      toast.success('Dataset saved successfully')
    }
  }, [resultData, resultHeaders, onSaveDataset])

  return (
    <div className="h-full w-full flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Workflow className="h-5 w-5 text-primary" />
              <h1 className="text-xl font-semibold">Data Pipeline Builder</h1>
            </div>
            <Badge variant="secondary" className="text-xs">
              {nodes.length} nodes • {edges.length} connections
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={executePipelineHandler}
              disabled={isExecuting || nodes.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              <Play className="h-4 w-4 mr-2" />
              {isExecuting ? 'Running Pipeline...' : 'Run Pipeline'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveAsDataset}
              disabled={resultData.length === 0}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Result
            </Button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex items-center gap-4 px-4 pb-4">
          {/* Dataset Selector */}
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Add Dataset:</span>
            <Select value={selectedDataset} onValueChange={setSelectedDataset}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select a dataset..." />
              </SelectTrigger>
              <SelectContent>
                {datasets.map((dataset) => (
                  <SelectItem key={dataset.id} value={dataset.id}>
                    <div className="flex items-center gap-2">
                      <Database className="h-3 w-3" />
                      <span>{dataset.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {dataset.rowCount} rows
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addDatasetNode()}
              disabled={!selectedDataset}
            >
              <Plus className="h-3 w-3 mr-1" />
              Add to Canvas
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Operation Buttons */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Operations:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addOperationNode('concat')}
              className="text-xs"
            >
              <Layers className="h-3 w-3 mr-1" />
              Concat
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addOperationNode('join')}
              className="text-xs"
            >
              <GitMerge className="h-3 w-3 mr-1" />
              Join
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addOperationNode('transform')}
              className="text-xs"
            >
              <Filter className="h-3 w-3 mr-1" />
              Transform
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addOperationNode('output')}
              className="text-xs"
            >
              <Target className="h-3 w-3 mr-1" />
              Output
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* View Controls */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">View:</span>
            <Button
              variant={showSQL ? "default" : "outline"}
              size="sm"
              onClick={() => setShowSQL(!showSQL)}
              className="text-xs"
            >
              <Code2 className="h-3 w-3 mr-1" />
              SQL
            </Button>
            <Button
              variant={showResults ? "default" : "outline"}
              size="sm"
              onClick={() => setShowResults(!showResults)}
              className="text-xs"
            >
              <Table2 className="h-3 w-3 mr-1" />
              Results
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Workflow Canvas */}
        <div className="flex-1 relative border-r">
          <Card className="h-full rounded-none border-0">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  <Workflow className="h-4 w-4" />
                  Pipeline Workflow
                </CardTitle>
                <div className="flex items-center gap-2">
                  {selectedNode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNodes(nodes.filter(n => n.id !== selectedNode))
                        setSelectedNode(null)
                      }}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete Node
                    </Button>
                  )}
                  <Badge variant="outline" className="text-xs">
                    {nodes.length === 0 ? 'Empty Canvas' : `${nodes.length} nodes`}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-4rem)]">
              {nodes.length === 0 ? (
                <div className="h-full flex items-center justify-center text-center">
                  <div className="space-y-4">
                    <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                      <Workflow className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Build Your Data Pipeline</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Select a dataset from the dropdown above and add it to the canvas to get started
                      </p>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                      <span>1. Add Dataset</span>
                      <span>→</span>
                      <span>2. Add Operations</span>
                      <span>→</span>
                      <span>3. Connect Nodes</span>
                      <span>→</span>
                      <span>4. Run Pipeline</span>
                    </div>
                  </div>
                </div>
              ) : (
                <ReactFlow
                  nodes={nodes}
                  edges={edges}
                  onNodesChange={onNodesChange}
                  onEdgesChange={onEdgesChange}
                  onConnect={onConnect}
                  onNodeClick={onNodeClick}
                  nodeTypes={nodeTypes}
                  fitView
                  fitViewOptions={{ padding: 0.1 }}
                  defaultEdgeOptions={{
                    type: 'smoothstep',
                    animated: true,
                    style: { strokeWidth: 2, stroke: '#6366f1' }
                  }}
                  className="bg-muted/20"
                >
                  <Background
                    variant={BackgroundVariant.Dots}
                    gap={20}
                    size={1}
                    color="#e2e8f0"
                  />
                  <Controls
                    className="bg-background border border-border rounded-lg shadow-sm"
                  />
                  <MiniMap
                    className="bg-background border border-border rounded-lg shadow-sm"
                    nodeColor="#6366f1"
                    maskColor="rgba(0, 0, 0, 0.1)"
                  />
                </ReactFlow>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Panels */}
        <div className="w-96 flex flex-col">
          {/* SQL Preview Panel */}
          {showSQL && (
            <div className="h-1/2 border-b">
              <Card className="h-full rounded-none border-0">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Code2 className="h-4 w-4" />
                    Generated SQL
                    <Badge variant="secondary" className="text-xs ml-auto">
                      Auto-generated
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 h-[calc(100%-4rem)]">
                  <SQLPreviewPanel sql={sqlPreview} />
                </CardContent>
              </Card>
            </div>
          )}

          {/* Results Panel */}
          {showResults && (
            <div className={showSQL ? "h-1/2" : "h-full"}>
              <Card className="h-full rounded-none border-0">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Table2 className="h-4 w-4" />
                    Pipeline Results
                    {resultData.length > 0 && (
                      <Badge variant="secondary" className="text-xs ml-auto">
                        {resultData.length} rows
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 h-[calc(100%-4rem)]">
                  <ResultTableView
                    data={resultData}
                    headers={resultHeaders}
                    onExport={() => {
                      // TODO: Implement CSV export
                      toast.info('Export functionality coming soon')
                    }}
                  />
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Wrap with ReactFlowProvider
export function DataPipelineWrapper(props: DataPipelineProps) {
  return (
    <ReactFlowProvider>
      <DataPipeline {...props} />
    </ReactFlowProvider>
  )
}