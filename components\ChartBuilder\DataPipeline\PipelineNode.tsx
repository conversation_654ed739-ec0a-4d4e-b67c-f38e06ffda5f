'use client'

import React from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Database, 
  GitMerge, 
  Filter, 
  Layers, 
  Target,
  Settings,
  Table
} from "lucide-react"
import { PipelineNodeData } from './types'

const nodeIcons = {
  dataset: Database,
  join: GitMerge,
  transform: Filter,
  concat: Layers,
  output: Target
}

const nodeColors = {
  dataset: 'bg-blue-50 border-blue-200 text-blue-700',
  join: 'bg-green-50 border-green-200 text-green-700',
  transform: 'bg-purple-50 border-purple-200 text-purple-700',
  concat: 'bg-orange-50 border-orange-200 text-orange-700',
  output: 'bg-red-50 border-red-200 text-red-700'
}

export function PipelineNode({ data, selected }: NodeProps<PipelineNodeData>) {
  const Icon = nodeIcons[data.type]
  const colorClass = nodeColors[data.type]

  return (
    <Card className={`min-w-[200px] ${colorClass} ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4" />
          <span className="font-medium text-sm">{data.label}</span>
          {data.type !== 'dataset' && data.type !== 'output' && (
            <Settings className="h-3 w-3 ml-auto opacity-50" />
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Dataset Info */}
        {data.type === 'dataset' && data.dataset && (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs">
              <Table className="h-3 w-3" />
              <span>{data.dataset.rowCount} rows</span>
            </div>
            <div className="text-xs opacity-75">
              {data.dataset.headers.length} columns
            </div>
            <div className="flex flex-wrap gap-1 mt-2">
              {data.dataset.headers.slice(0, 3).map((header) => (
                <Badge key={header} variant="secondary" className="text-xs px-1 py-0">
                  {header}
                </Badge>
              ))}
              {data.dataset.headers.length > 3 && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  +{data.dataset.headers.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Join Node Info */}
        {data.type === 'join' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.joinType || 'inner'}
            </div>
            {data.config.joinKeys && data.config.joinKeys.length > 0 && (
              <div className="text-xs opacity-75">
                Keys: {data.config.joinKeys.length}
              </div>
            )}
          </div>
        )}

        {/* Transform Node Info */}
        {data.type === 'transform' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.transformType || 'filter'}
            </div>
            {data.config.filters && data.config.filters.length > 0 && (
              <div className="text-xs opacity-75">
                Filters: {data.config.filters.length}
              </div>
            )}
          </div>
        )}

        {/* Concat Node Info */}
        {data.type === 'concat' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.concatType || 'union'}
            </div>
          </div>
        )}

        {/* Output Node Info */}
        {data.type === 'output' && data.resultData && (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs">
              <Table className="h-3 w-3" />
              <span>{data.resultData.length} rows</span>
            </div>
            {data.resultHeaders && (
              <div className="text-xs opacity-75">
                {data.resultHeaders.length} columns
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Handles */}
      {data.type !== 'dataset' && (
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
        />
      )}
      
      {data.type !== 'output' && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
        />
      )}
    </Card>
  )
}
