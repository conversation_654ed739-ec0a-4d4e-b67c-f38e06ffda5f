'use client'

import React from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Database, 
  GitMerge, 
  Filter, 
  Layers, 
  Target,
  Settings,
  Table
} from "lucide-react"
import { PipelineNodeData } from './types'

const nodeIcons = {
  dataset: Database,
  join: GitMerge,
  transform: Filter,
  concat: Layers,
  output: Target
}

const nodeColors = {
  dataset: 'bg-blue-50 border-blue-300 text-blue-800 dark:bg-blue-950 dark:border-blue-700 dark:text-blue-200',
  join: 'bg-green-50 border-green-300 text-green-800 dark:bg-green-950 dark:border-green-700 dark:text-green-200',
  transform: 'bg-purple-50 border-purple-300 text-purple-800 dark:bg-purple-950 dark:border-purple-700 dark:text-purple-200',
  concat: 'bg-orange-50 border-orange-300 text-orange-800 dark:bg-orange-950 dark:border-orange-700 dark:text-orange-200',
  output: 'bg-emerald-50 border-emerald-300 text-emerald-800 dark:bg-emerald-950 dark:border-emerald-700 dark:text-emerald-200'
}

export function PipelineNode({ data, selected }: NodeProps<PipelineNodeData>) {
  const Icon = nodeIcons[data.type]
  const colorClass = nodeColors[data.type]

  return (
    <Card className={`min-w-[220px] max-w-[280px] ${colorClass} ${selected ? 'ring-2 ring-primary shadow-lg' : 'shadow-sm'} transition-all duration-200 hover:shadow-md`}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-white/50 dark:bg-black/20">
            <Icon className="h-4 w-4" />
          </div>
          <div className="flex-1">
            <span className="font-semibold text-sm">{data.label}</span>
            <div className="text-xs opacity-75 capitalize">
              {data.type} node
            </div>
          </div>
          {data.type !== 'dataset' && data.type !== 'output' && (
            <Settings className="h-3 w-3 opacity-50" />
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Dataset Info */}
        {data.type === 'dataset' && data.dataset && (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs">
              <Table className="h-3 w-3" />
              <span>{data.dataset.rowCount} rows</span>
            </div>
            <div className="text-xs opacity-75">
              {data.dataset.headers.length} columns
            </div>
            <div className="flex flex-wrap gap-1 mt-2">
              {data.dataset.headers.slice(0, 3).map((header) => (
                <Badge key={header} variant="secondary" className="text-xs px-1 py-0">
                  {header}
                </Badge>
              ))}
              {data.dataset.headers.length > 3 && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  +{data.dataset.headers.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Join Node Info */}
        {data.type === 'join' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.joinType || 'inner'}
            </div>
            {data.config.joinKeys && data.config.joinKeys.length > 0 && (
              <div className="text-xs opacity-75">
                Keys: {data.config.joinKeys.length}
              </div>
            )}
          </div>
        )}

        {/* Transform Node Info */}
        {data.type === 'transform' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.transformType || 'filter'}
            </div>
            {data.config.filters && data.config.filters.length > 0 && (
              <div className="text-xs opacity-75">
                Filters: {data.config.filters.length}
              </div>
            )}
          </div>
        )}

        {/* Concat Node Info */}
        {data.type === 'concat' && data.config && (
          <div className="space-y-1">
            <div className="text-xs">
              Type: {data.config.concatType || 'union'}
            </div>
          </div>
        )}

        {/* Output Node Info */}
        {data.type === 'output' && data.resultData && (
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs">
              <Table className="h-3 w-3" />
              <span>{data.resultData.length} rows</span>
            </div>
            {data.resultHeaders && (
              <div className="text-xs opacity-75">
                {data.resultHeaders.length} columns
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Handles */}
      {data.type !== 'dataset' && (
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
        />
      )}
      
      {data.type !== 'output' && (
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
        />
      )}
    </Card>
  )
}
