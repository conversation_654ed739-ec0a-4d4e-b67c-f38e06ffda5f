import { Node, Edge } from 'reactflow';

export interface Dataset {
  id: string;
  name: string;
  headers: string[];
  data: any[];
  rowCount: number;
  fileType: string;
  description?: string;
}

export interface PipelineNodeData {
  id: string;
  type: 'dataset' | 'join' | 'transform' | 'concat' | 'output';
  label: string;
  dataset?: Dataset;
  config?: {
    // For join nodes
    joinType?: 'inner' | 'left' | 'right' | 'full';
    joinKeys?: { left: string; right: string }[];
    
    // For transform nodes
    transformType?: 'filter' | 'select' | 'aggregate' | 'sort';
    filters?: { column: string; operator: string; value: any }[];
    selectedColumns?: string[];
    aggregations?: { column: string; function: string }[];
    sortBy?: { column: string; direction: 'asc' | 'desc' }[];
    
    // For concat nodes
    concatType?: 'union' | 'append';
  };
  resultData?: any[];
  resultHeaders?: string[];
}

export type PipelineNode = Node<PipelineNodeData>;
export type PipelineEdge = Edge;

export interface PipelineState {
  nodes: PipelineNode[];
  edges: PipelineEdge[];
  selectedNode: string | null;
  sqlPreview: string;
  resultData: any[];
  resultHeaders: string[];
}

export interface DataPipelineProps {
  datasets: Dataset[];
  onSaveDataset?: (dataset: Dataset) => void;
}
