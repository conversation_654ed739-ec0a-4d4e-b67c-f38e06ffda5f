'use client'

import React, { useState, useMemo } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  Download, 
  Search, 
  ChevronLeft, 
  ChevronRight,
  Database
} from "lucide-react"

interface ResultTableViewProps {
  data: any[]
  headers: string[]
  onExport: () => void
}

const ROWS_PER_PAGE = 50

export function ResultTableView({ data, headers, onExport }: ResultTableViewProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return data
    
    return data.filter(row =>
      headers.some(header => {
        const value = row[header]
        return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      })
    )
  }, [data, headers, searchTerm])

  // Paginate filtered data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * ROWS_PER_PAGE
    return filteredData.slice(startIndex, startIndex + ROWS_PER_PAGE)
  }, [filteredData, currentPage])

  const totalPages = Math.ceil(filteredData.length / ROWS_PER_PAGE)

  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1))
  }

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1))
  }

  return (
    <div className="h-full flex flex-col min-h-[300px] max-h-[500px]">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Data Preview</span>
          {data.length > 0 && (
            <Badge variant="outline" className="text-xs">
              {filteredData.length} / {data.length} rows
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {/* Search */}
          {data.length > 0 && (
            <div className="relative">
              <Search className="h-3 w-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search data..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1) // Reset to first page when searching
                }}
                className="pl-7 h-7 w-28 text-xs"
              />
            </div>
          )}

          {/* Export Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onExport}
            disabled={data.length === 0}
            className="h-7 px-2"
          >
            <Download className="h-3 w-3 mr-1" />
            Export
          </Button>
        </div>
      </div>
      
      {/* Content */}
      <div className="flex-1 flex flex-col p-3">
        {data.length === 0 ? (
          <div className="flex-1 flex items-center justify-center text-center">
            <div className="space-y-3">
              <div className="w-12 h-12 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Table className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <p className="text-sm font-medium">No Results Yet</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Run your pipeline to see the data results here
                </p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Data Info */}
            <div className="flex items-center justify-between text-xs text-muted-foreground bg-muted/20 px-3 py-2 rounded mb-3">
              <span>
                Combined data from multiple sources. Empty cells (—) indicate data not present in that source.
              </span>
              <span>
                {data.length} total rows
              </span>
            </div>

            {/* Table */}
            <div className="flex-1 overflow-auto border rounded-md bg-background min-h-[200px] max-h-[350px]">
              <table className="w-full text-sm">
                <thead className="bg-muted/50 sticky top-0 z-10">
                  <tr>
                    <th className="px-2 py-2 text-left text-xs font-medium text-foreground border-b w-8">
                      #
                    </th>
                    {headers.map((header) => (
                      <th
                        key={header}
                        className="px-3 py-2 text-left text-xs font-medium text-foreground border-b whitespace-nowrap"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {paginatedData.map((row, index) => {
                    const globalIndex = (currentPage - 1) * ROWS_PER_PAGE + index + 1
                    const hasData = headers.some(header =>
                      row[header] !== null && row[header] !== undefined && row[header] !== ''
                    )

                    return (
                      <tr
                        key={index}
                        className={`border-b hover:bg-muted/25 transition-colors ${
                          !hasData ? 'opacity-60' : ''
                        }`}
                      >
                        <td className="px-2 py-2 text-xs text-muted-foreground border-r">
                          {globalIndex}
                        </td>
                        {headers.map((header) => (
                          <td
                            key={header}
                            className="px-3 py-2 text-xs max-w-[200px] truncate"
                            title={row[header]?.toString() || 'No data'}
                          >
                            {row[header] !== null && row[header] !== undefined && row[header] !== '' ? (
                              <span className="text-foreground">
                                {row[header].toString()}
                              </span>
                            ) : (
                              <span className="text-muted-foreground/30 italic text-xs">
                                —
                              </span>
                            )}
                          </td>
                        ))}
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-3 pt-3 border-t">
                <div className="text-xs text-muted-foreground">
                  Showing {((currentPage - 1) * ROWS_PER_PAGE) + 1} to {Math.min(currentPage * ROWS_PER_PAGE, filteredData.length)} of {filteredData.length} rows
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                    className="h-7 px-2"
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  <span className="text-xs text-muted-foreground px-2">
                    {currentPage} / {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                    className="h-7 px-2"
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  ) 
}
