'use client'

import React, { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  Columns, 
  Check, 
  X, 
  Search,
  Database,
  ArrowRight
} from "lucide-react"
import { Input } from "@/components/ui/input"

interface ColumnSelectorProps {
  dataset: {
    id: string
    name: string
    headers: string[]
    data: any[]
  }
  selectedColumns: string[]
  onColumnsChange: (columns: string[]) => void
  onClose: () => void
  onApply: () => void
}

export function ColumnSelector({ 
  dataset, 
  selectedColumns, 
  onColumnsChange, 
  onClose, 
  onApply 
}: ColumnSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('')

  // Filter columns based on search
  const filteredColumns = dataset.headers.filter(header =>
    header.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get sample data for preview
  const getSampleData = (column: string) => {
    return dataset.data.slice(0, 3).map(row => row[column]).filter(val => val != null)
  }

  // Toggle column selection
  const toggleColumn = (column: string) => {
    if (selectedColumns.includes(column)) {
      onColumnsChange(selectedColumns.filter(col => col !== column))
    } else {
      onColumnsChange([...selectedColumns, column])
    }
  }

  // Select all filtered columns
  const selectAll = () => {
    const newSelection = [...new Set([...selectedColumns, ...filteredColumns])]
    onColumnsChange(newSelection)
  }

  // Deselect all filtered columns
  const deselectAll = () => {
    onColumnsChange(selectedColumns.filter(col => !filteredColumns.includes(col)))
  }

  return (
    <Card className="w-96 max-h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <Columns className="h-4 w-4" />
            Select Columns
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Database className="h-3 w-3" />
          <span>{dataset.name}</span>
          <Badge variant="outline" className="text-xs">
            {dataset.data.length} rows
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-3">
        {/* Search */}
        <div className="relative">
          <Search className="h-3 w-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search columns..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-7 h-8 text-xs"
          />
        </div>

        {/* Selection Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={selectAll} className="h-7 text-xs">
              Select All
            </Button>
            <Button variant="outline" size="sm" onClick={deselectAll} className="h-7 text-xs">
              Clear
            </Button>
          </div>
          <Badge variant="secondary" className="text-xs">
            {selectedColumns.length} selected
          </Badge>
        </div>

        <Separator />

        {/* Column List */}
        <ScrollArea className="flex-1">
          <div className="space-y-2">
            {filteredColumns.map((column) => {
              const isSelected = selectedColumns.includes(column)
              const sampleData = getSampleData(column)
              
              return (
                <div
                  key={column}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    isSelected 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => toggleColumn(column)}
                >
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => toggleColumn(column)}
                      className="mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{column}</span>
                        {isSelected && (
                          <Check className="h-3 w-3 text-primary" />
                        )}
                      </div>
                      {sampleData.length > 0 && (
                        <div className="mt-1">
                          <div className="text-xs text-muted-foreground">
                            Sample: {sampleData.slice(0, 2).map(val => 
                              String(val).length > 15 
                                ? String(val).substring(0, 15) + '...' 
                                : String(val)
                            ).join(', ')}
                            {sampleData.length > 2 && '...'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </ScrollArea>

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t">
          <Button variant="outline" onClick={onClose} className="text-xs">
            Cancel
          </Button>
          <Button 
            onClick={onApply} 
            disabled={selectedColumns.length === 0}
            className="text-xs"
          >
            <ArrowRight className="h-3 w-3 mr-1" />
            Apply Selection
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
