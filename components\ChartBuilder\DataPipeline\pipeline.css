/* Data Pipeline Styles */

/* Ensure proper height inheritance */
.data-pipeline-container {
  height: 100vh;
  max-height: calc(100vh - 120px); /* Account for header and tabs */
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Fix for TabsContent height inheritance */
[data-state="active"][data-orientation="horizontal"] {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Ensure ResizablePanelGroup takes full height */
.react-resizable-panels {
  height: 100% !important;
}

/* Fix for ReactFlow container */
.react-flow {
  height: 100% !important;
  width: 100% !important;
}

.react-flow__node {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid hsl(var(--border));
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px hsl(var(--primary)), 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
  transition: all 0.2s ease;
}

.react-flow__edge {
  stroke-width: 3;
  stroke: hsl(var(--primary));
  opacity: 0.8;
}

.react-flow__edge.selected {
  stroke: hsl(var(--primary));
  opacity: 1;
  stroke-width: 4;
}

.react-flow__edge-path {
  stroke-dasharray: none;
  animation: flow 2s ease-in-out infinite;
}

@keyframes flow {
  0%, 100% { stroke-dasharray: 5, 5; }
  50% { stroke-dasharray: 10, 5; }
}

.react-flow__handle {
  width: 14px;
  height: 14px;
  border: 3px solid hsl(var(--background));
  background: hsl(var(--primary));
  opacity: 0.8;
  transition: all 0.2s ease;
}

.react-flow__handle.connectable:hover {
  background: hsl(var(--primary));
  opacity: 1;
  transform: scale(1.2);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

.react-flow__handle-left {
  left: -7px;
}

.react-flow__handle-right {
  right: -7px;
}

/* Pipeline toolbar */
.pipeline-toolbar {
  background: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* SQL Preview Panel */
.sql-preview {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: hsl(var(--muted));
  border-radius: 6px;
  padding: 12px;
  overflow: auto;
  white-space: pre-wrap;
  color: hsl(var(--muted-foreground));
}

/* Result table */
.result-table {
  border-collapse: collapse;
  width: 100%;
}

.result-table th,
.result-table td {
  border: 1px solid hsl(var(--border));
  padding: 8px 12px;
  text-align: left;
}

.result-table th {
  background: hsl(var(--muted));
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 1;
}

.result-table tr:hover {
  background: hsl(var(--muted) / 0.5);
}

/* Pipeline specific styles */
.pipeline-canvas {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--background)) 100%);
}

.pipeline-empty-state {
  background: radial-gradient(circle at center, hsl(var(--muted) / 0.3) 0%, transparent 70%);
}

.pipeline-header {
  background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted) / 0.5) 100%);
  backdrop-filter: blur(10px);
}

.pipeline-panel {
  backdrop-filter: blur(10px);
  background: hsl(var(--card) / 0.95);
}

/* Controls styling */
.react-flow__controls {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.react-flow__controls-button {
  background: hsl(var(--background));
  border: none;
  border-bottom: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
}

.react-flow__controls-button:hover {
  background: hsl(var(--muted));
  transform: scale(1.05);
}

/* MiniMap styling */
.react-flow__minimap {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pipeline-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .pipeline-toolbar > div {
    flex-wrap: wrap;
    justify-content: center;
  }

  .react-flow__controls {
    bottom: 20px;
    left: 20px;
  }

  .react-flow__minimap {
    bottom: 20px;
    right: 20px;
    width: 120px;
    height: 80px;
  }
}
