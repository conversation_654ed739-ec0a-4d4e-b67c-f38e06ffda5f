/* Data Pipeline Styles */

.react-flow__node {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px hsl(var(--primary));
}

.react-flow__edge {
  stroke-width: 2;
  stroke: hsl(var(--muted-foreground));
}

.react-flow__edge.selected {
  stroke: hsl(var(--primary));
}

.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid hsl(var(--background));
  background: hsl(var(--muted-foreground));
}

.react-flow__handle.connectable:hover {
  background: hsl(var(--primary));
}

/* Pipeline toolbar */
.pipeline-toolbar {
  background: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* SQL Preview Panel */
.sql-preview {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  background: hsl(var(--muted));
  border-radius: 6px;
  padding: 12px;
  overflow: auto;
  white-space: pre-wrap;
  color: hsl(var(--muted-foreground));
}

/* Result table */
.result-table {
  border-collapse: collapse;
  width: 100%;
}

.result-table th,
.result-table td {
  border: 1px solid hsl(var(--border));
  padding: 8px 12px;
  text-align: left;
}

.result-table th {
  background: hsl(var(--muted));
  font-weight: 500;
  position: sticky;
  top: 0;
  z-index: 1;
}

.result-table tr:hover {
  background: hsl(var(--muted) / 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pipeline-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .pipeline-toolbar > div {
    flex-wrap: wrap;
    justify-content: center;
  }
}
