'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Settings, 
  X, 
  Columns, 
  Filter, 
  GitMerge,
  Layers,
  Check,
  Plus,
  Trash2
} from "lucide-react"
import { PipelineNodeData } from './types'
import { ColumnSelector } from './ColumnSelector'

interface FilterConfig {
  column: string
  operator: string
  value: string
}

interface NodeConfig {
  transformType?: 'select' | 'filter'
  selectedColumns?: string[]
  filters?: FilterConfig[]
  joinType?: 'inner' | 'left' | 'right' | 'full'
  joinKeys?: { left: string; right: string }[]
  concatType?: 'union' | 'append'
}

interface NodeConfigPanelProps {
  node: {
    id: string
    data: PipelineNodeData
  }
  availableDatasets: Array<{
    id: string
    name: string
    headers: string[]
    data: any[]
  }>
  allNodes: Array<{
    id: string
    data: PipelineNodeData
  }>
  edges: Array<{
    source: string
    target: string
  }>
  onConfigChange: (nodeId: string, config: NodeConfig) => void
  onClose: () => void
}

export function NodeConfigPanel({
  node,
  availableDatasets,
  allNodes,
  edges,
  onConfigChange,
  onClose
}: NodeConfigPanelProps) {
  const [config, setConfig] = useState<NodeConfig>(node.data.config || {})
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<any>(null)

  // Get the source dataset for this node by finding connected input nodes
  useEffect(() => {
    console.log('NodeConfigPanel: Checking dataset connection for node:', node.id, {
      nodeType: node.data.type,
      hasEdges: !!edges,
      hasAllNodes: !!allNodes,
      edgesCount: edges?.length || 0,
      nodesCount: allNodes?.length || 0
    })

    if (node.data.type === 'transform' && edges && allNodes) {
      // Find incoming edges to this node
      const incomingEdges = edges.filter(edge => edge.target === node.id)

      console.log('NodeConfigPanel: Found incoming edges:', incomingEdges)

      if (incomingEdges.length > 0) {
        // Get the source node
        const sourceNodeId = incomingEdges[0].source
        const sourceNode = allNodes.find(n => n.id === sourceNodeId)

        console.log('NodeConfigPanel: Source node:', sourceNode)

        if (sourceNode) {
          if (sourceNode.data.type === 'dataset' && sourceNode.data.dataset) {
            // Direct dataset connection
            console.log('NodeConfigPanel: Setting dataset from direct connection:', sourceNode.data.dataset.name)
            setSelectedDataset(sourceNode.data.dataset)
          } else {
            // Try to find dataset from available datasets based on node data
            const dataset = availableDatasets.find(d =>
              sourceNode.data.dataset?.id === d.id ||
              sourceNode.id.includes(d.id)
            )
            if (dataset) {
              console.log('NodeConfigPanel: Setting dataset from available datasets:', dataset.name)
              setSelectedDataset(dataset)
            } else {
              console.log('NodeConfigPanel: No matching dataset found')
            }
          }
        }
      } else {
        console.log('NodeConfigPanel: No incoming edges found')
        setSelectedDataset(null)
      }
    }
  }, [node, allNodes, edges, availableDatasets])

  const updateConfig = (updates: Partial<NodeConfig>) => {
    const newConfig: NodeConfig = { ...config, ...updates }
    setConfig(newConfig)
    onConfigChange(node.id, newConfig)
  }

  const renderTransformConfig = () => {
    if (!selectedDataset) {
      return (
        <div className="text-sm text-muted-foreground p-3 border border-dashed rounded-md">
          Connect a dataset node to configure transforms
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {/* Transform Type */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Transform Type</Label>
          <Select
            value={config.transformType || 'select'}
            onValueChange={(value) => updateConfig({ transformType: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="select">
                <div className="flex items-center gap-2">
                  <Columns className="h-3 w-3" />
                  <span className="text-xs">Select Columns</span>
                </div>
              </SelectItem>
              <SelectItem value="filter">
                <div className="flex items-center gap-2">
                  <Filter className="h-3 w-3" />
                  <span className="text-xs">Filter Rows</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Column Selection */}
        {config.transformType === 'select' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Selected Columns</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowColumnSelector(true)}
                disabled={!selectedDataset}
                className="h-7 text-xs px-2"
              >
                <Columns className="h-3 w-3 mr-1" />
                Choose
              </Button>
            </div>

            {config.selectedColumns && Array.isArray(config.selectedColumns) && config.selectedColumns.length > 0 ? (
              <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                {config.selectedColumns.map((column: string) => (
                  <Badge key={column} variant="secondary" className="text-xs">
                    {column}
                    <button
                      onClick={() => {
                        const currentColumns = config.selectedColumns || []
                        const newColumns = currentColumns.filter((c: string) => c !== column)
                        updateConfig({ selectedColumns: newColumns })
                      }}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-2 w-2" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
                Click "Choose" to select columns
              </div>
            )}
          </div>
        )}

        {/* Filter Configuration */}
        {config.transformType === 'filter' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Filters</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentFilters = config.filters || []
                  const newFilters = [...currentFilters, { column: '', operator: '=', value: '' }]
                  updateConfig({ filters: newFilters })
                }}
                className="h-7 text-xs px-2"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
            </div>

            {config.filters && Array.isArray(config.filters) && config.filters.length > 0 ? (
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {config.filters.map((filter: FilterConfig, index: number) => (
                  <div key={index} className="flex items-center gap-1 p-2 border rounded text-xs">
                    <Select
                      value={filter.column || ''}
                      onValueChange={(value) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, column: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-20 h-7">
                        <SelectValue placeholder="Col" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedDataset?.headers?.map((header: string) => (
                          <SelectItem key={header} value={header}>
                            {header}
                          </SelectItem>
                        )) || []}
                      </SelectContent>
                    </Select>

                    <Select
                      value={filter.operator || '='}
                      onValueChange={(value) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, operator: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-16 h-7">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="=">=</SelectItem>
                        <SelectItem value="!=">!=</SelectItem>
                        <SelectItem value=">">&gt;</SelectItem>
                        <SelectItem value="<">&lt;</SelectItem>
                        <SelectItem value="contains">has</SelectItem>
                      </SelectContent>
                    </Select>

                    <Input
                      placeholder="Value"
                      value={filter.value || ''}
                      onChange={(e) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, value: e.target.value }
                        updateConfig({ filters: newFilters })
                      }}
                      className="flex-1 h-7 text-xs"
                    />

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentFilters = config.filters || []
                        const newFilters = currentFilters.filter((_: FilterConfig, i: number) => i !== index)
                        updateConfig({ filters: newFilters })
                      }}
                      className="h-7 w-7 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
                Click "Add" to create filters
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  const renderJoinConfig = () => (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label className="text-xs font-medium">Join Type</Label>
        <Select
          value={config.joinType || 'inner'}
          onValueChange={(value) => updateConfig({ joinType: value })}
        >
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="inner">
              <div className="flex items-center gap-2">
                <GitMerge className="h-3 w-3" />
                <span className="text-xs">Inner Join</span>
              </div>
            </SelectItem>
            <SelectItem value="left">
              <div className="flex items-center gap-2">
                <GitMerge className="h-3 w-3" />
                <span className="text-xs">Left Join</span>
              </div>
            </SelectItem>
            <SelectItem value="right">
              <div className="flex items-center gap-2">
                <GitMerge className="h-3 w-3" />
                <span className="text-xs">Right Join</span>
              </div>
            </SelectItem>
            <SelectItem value="full">
              <div className="flex items-center gap-2">
                <GitMerge className="h-3 w-3" />
                <span className="text-xs">Full Join</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
        Connect two datasets to configure join keys
      </div>
    </div>
  )

  const renderConcatConfig = () => (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label className="text-xs font-medium">Concat Type</Label>
        <Select
          value={config.concatType || 'union'}
          onValueChange={(value) => updateConfig({ concatType: value })}
        >
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="union">
              <div className="flex items-center gap-2">
                <Layers className="h-3 w-3" />
                <div className="flex flex-col">
                  <span className="text-xs font-medium">Union</span>
                  <span className="text-xs text-muted-foreground">Remove duplicates</span>
                </div>
              </div>
            </SelectItem>
            <SelectItem value="append">
              <div className="flex items-center gap-2">
                <Layers className="h-3 w-3" />
                <div className="flex flex-col">
                  <span className="text-xs font-medium">Append</span>
                  <span className="text-xs text-muted-foreground">Keep all rows</span>
                </div>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
        Connect multiple datasets to combine
      </div>
    </div>
  )

  return (
    <>
      <Card className="w-72 sm:w-80 max-h-[500px] sm:max-h-[600px] flex flex-col shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Configure</span> {node.data.type}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose} className="h-7 w-7 p-0">
              <X className="h-3 w-3" />
            </Button>
          </div>
          <Badge variant="outline" className="text-xs capitalize w-fit">
            {node.data.type} node
          </Badge>
        </CardHeader>

        <CardContent className="flex-1 p-3">
          <ScrollArea className="h-full pr-2">
            {node.data.type === 'transform' && renderTransformConfig()}
            {node.data.type === 'join' && renderJoinConfig()}
            {node.data.type === 'concat' && renderConcatConfig()}
            {node.data.type === 'output' && (
              <div className="text-xs text-muted-foreground p-3 border border-dashed rounded text-center">
                Output nodes display the final pipeline result. No configuration needed.
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Column Selector Modal */}
      {showColumnSelector && selectedDataset && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <ColumnSelector
            dataset={selectedDataset}
            selectedColumns={config.selectedColumns || []}
            onColumnsChange={(columns) => updateConfig({ selectedColumns: columns })}
            onClose={() => setShowColumnSelector(false)}
            onApply={() => setShowColumnSelector(false)}
          />
        </div>
      )}
    </>
  )
}
