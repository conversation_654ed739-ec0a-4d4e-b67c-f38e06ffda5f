'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Settings, 
  X, 
  Columns, 
  Filter, 
  GitMerge,
  Layers,
  Check,
  Plus,
  Trash2
} from "lucide-react"
import { PipelineNodeData } from './types'
import { ColumnSelector } from './ColumnSelector'

interface NodeConfigPanelProps {
  node: {
    id: string
    data: PipelineNodeData
  }
  availableDatasets: Array<{
    id: string
    name: string
    headers: string[]
    data: any[]
  }>
  onConfigChange: (nodeId: string, config: any) => void
  onClose: () => void
}

export function NodeConfigPanel({ 
  node, 
  availableDatasets, 
  onConfigChange, 
  onClose 
}: NodeConfigPanelProps) {
  const [config, setConfig] = useState(node.data.config || {})
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<any>(null)

  // Get the source dataset for this node
  useEffect(() => {
    if (node.data.type === 'transform' && node.data.dataset) {
      setSelectedDataset(node.data.dataset)
    }
  }, [node])

  const updateConfig = (updates: any) => {
    const newConfig = { ...config, ...updates }
    setConfig(newConfig)
    onConfigChange(node.id, newConfig)
  }

  const renderTransformConfig = () => {
    if (!selectedDataset) return null

    return (
      <div className="space-y-4">
        {/* Transform Type */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Transform Type</Label>
          <Select 
            value={config.transformType || 'select'} 
            onValueChange={(value) => updateConfig({ transformType: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="select">
                <div className="flex items-center gap-2">
                  <Columns className="h-3 w-3" />
                  Select Columns
                </div>
              </SelectItem>
              <SelectItem value="filter">
                <div className="flex items-center gap-2">
                  <Filter className="h-3 w-3" />
                  Filter Rows
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Column Selection */}
        {config.transformType === 'select' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Selected Columns</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowColumnSelector(true)}
                className="h-7 text-xs"
              >
                <Columns className="h-3 w-3 mr-1" />
                Choose Columns
              </Button>
            </div>
            
            {config.selectedColumns && config.selectedColumns.length > 0 ? (
              <div className="flex flex-wrap gap-1">
                {config.selectedColumns.map((column: string) => (
                  <Badge key={column} variant="secondary" className="text-xs">
                    {column}
                    <button
                      onClick={() => {
                        const newColumns = config.selectedColumns.filter((c: string) => c !== column)
                        updateConfig({ selectedColumns: newColumns })
                      }}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-2 w-2" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded">
                No columns selected. Click "Choose Columns" to select.
              </div>
            )}
          </div>
        )}

        {/* Filter Configuration */}
        {config.transformType === 'filter' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Filters</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newFilters = [...(config.filters || []), { column: '', operator: '=', value: '' }]
                  updateConfig({ filters: newFilters })
                }}
                className="h-7 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Filter
              </Button>
            </div>

            {config.filters && config.filters.length > 0 ? (
              <div className="space-y-2">
                {config.filters.map((filter: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 p-2 border rounded">
                    <Select
                      value={filter.column}
                      onValueChange={(value) => {
                        const newFilters = [...config.filters]
                        newFilters[index] = { ...filter, column: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue placeholder="Column" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedDataset.headers.map((header: string) => (
                          <SelectItem key={header} value={header}>
                            {header}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select
                      value={filter.operator}
                      onValueChange={(value) => {
                        const newFilters = [...config.filters]
                        newFilters[index] = { ...filter, operator: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="=">=</SelectItem>
                        <SelectItem value="!=">!=</SelectItem>
                        <SelectItem value=">">></SelectItem>
                        <SelectItem value="<"><</SelectItem>
                        <SelectItem value="contains">contains</SelectItem>
                      </SelectContent>
                    </Select>

                    <Input
                      placeholder="Value"
                      value={filter.value}
                      onChange={(e) => {
                        const newFilters = [...config.filters]
                        newFilters[index] = { ...filter, value: e.target.value }
                        updateConfig({ filters: newFilters })
                      }}
                      className="flex-1 h-8 text-xs"
                    />

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newFilters = config.filters.filter((_: any, i: number) => i !== index)
                        updateConfig({ filters: newFilters })
                      }}
                      className="h-8 w-8 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded">
                No filters configured. Click "Add Filter" to create one.
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  const renderJoinConfig = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label className="text-sm font-medium">Join Type</Label>
        <Select 
          value={config.joinType || 'inner'} 
          onValueChange={(value) => updateConfig({ joinType: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="inner">Inner Join</SelectItem>
            <SelectItem value="left">Left Join</SelectItem>
            <SelectItem value="right">Right Join</SelectItem>
            <SelectItem value="full">Full Outer Join</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="text-xs text-muted-foreground p-2 border border-dashed rounded">
        Connect two dataset nodes to configure join keys
      </div>
    </div>
  )

  const renderConcatConfig = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label className="text-sm font-medium">Concat Type</Label>
        <Select 
          value={config.concatType || 'union'} 
          onValueChange={(value) => updateConfig({ concatType: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="union">Union (Remove Duplicates)</SelectItem>
            <SelectItem value="append">Append (Keep All Rows)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="text-xs text-muted-foreground p-2 border border-dashed rounded">
        Connect multiple dataset nodes to combine them
      </div>
    </div>
  )

  return (
    <>
      <Card className="w-80 max-h-[600px] flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configure {node.data.type}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs capitalize">
              {node.data.type} node
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="flex-1">
          <ScrollArea className="h-full">
            {node.data.type === 'transform' && renderTransformConfig()}
            {node.data.type === 'join' && renderJoinConfig()}
            {node.data.type === 'concat' && renderConcatConfig()}
            {node.data.type === 'output' && (
              <div className="text-sm text-muted-foreground">
                Output nodes don't require configuration. They display the final result of your pipeline.
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Column Selector Modal */}
      {showColumnSelector && selectedDataset && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <ColumnSelector
            dataset={selectedDataset}
            selectedColumns={config.selectedColumns || []}
            onColumnsChange={(columns) => updateConfig({ selectedColumns: columns })}
            onClose={() => setShowColumnSelector(false)}
            onApply={() => setShowColumnSelector(false)}
          />
        </div>
      )}
    </>
  )
}
