'use client'

import React, { useState, useEffect } from 'react'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Settings,
  X,
  Columns,
  Filter,
  GitMerge,
  Layers,
  Plus,
  Trash2,
  DatabaseBackup
} from "lucide-react"
import { PipelineNodeData } from './types'
import { ColumnSelector } from './ColumnSelector'

interface FilterConfig {
  column: string
  operator: string
  value: string
}

interface NodeConfig {
  transformType?: 'select' | 'filter'
  selectedColumns?: string[]
  filters?: FilterConfig[]
  joinType?: 'inner' | 'left' | 'right' | 'full'
  joinKeys?: { left: string; right: string }[]
  concatType?: 'union' | 'append'
  columnSelection?: { [datasetId: string]: string[] }
}

interface NodeConfigPanelProps {
  node: {
    id: string
    data: PipelineNodeData
  }
  availableDatasets: Array<{
    id: string
    name: string
    headers: string[]
    data: any[]
    rowCount?: number
    fileType?: string
    description?: string
  }>
  allNodes: Array<{
    id: string
    data: PipelineNodeData
  }>
  edges: Array<{
    source: string
    target: string
  }>
  onConfigChange: (nodeId: string, config: NodeConfig) => void
  onClose: () => void
}

export function NodeConfigPanel({
  node,
  availableDatasets,
  allNodes,
  edges,
  onConfigChange,
  onClose
}: NodeConfigPanelProps) {
  const [config, setConfig] = useState<NodeConfig>(() => {
    // Safe access to node.data.config with proper fallback
    const nodeConfig = node?.data?.config || {}
    return nodeConfig as NodeConfig
  })
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<any>(null)

  // Get the source datasets for this node by finding connected input nodes
  const [connectedDatasets, setConnectedDatasets] = useState<any[]>([])

  useEffect(() => {
    if ((node.data.type === 'transform' || node.data.type === 'concat' || node.data.type === 'join') && edges && allNodes) {
      // Find incoming edges to this node
      const incomingEdges = edges.filter(edge => edge.target === node.id)

      if (incomingEdges.length > 0) {
        const datasets: any[] = []

        // Get all source nodes and their datasets
        for (const edge of incomingEdges) {
          const sourceNode = allNodes.find(n => n.id === edge.source)

          if (sourceNode) {
            if (sourceNode.data.type === 'dataset' && sourceNode.data.dataset) {
              // Direct dataset connection
              datasets.push(sourceNode.data.dataset)
            } else {
              // Try to find dataset from available datasets based on node data
              const dataset = availableDatasets.find(d =>
                sourceNode.data.dataset?.id === d.id ||
                sourceNode.id.includes(d.id)
              )
              if (dataset) {
                datasets.push(dataset)
              }
            }
          }
        }

        setConnectedDatasets(datasets)
        // For transform nodes, set the first dataset as selected
        if (node.data.type === 'transform' && datasets.length > 0) {
          setSelectedDataset(datasets[0])
        }
      } else {
        setConnectedDatasets([])
        setSelectedDataset(null)
      }
    }
  }, [node, allNodes, edges, availableDatasets])

  const updateConfig = (updates: Partial<NodeConfig>) => {
    const newConfig: NodeConfig = { ...config, ...updates }
    setConfig(newConfig)
    onConfigChange(node.id, newConfig)
  }

  const renderTransformConfig = () => {
    if (!selectedDataset) {
      return (
        <div className="text-sm text-muted-foreground p-3 border border-dashed rounded-md">
          Connect a dataset node to configure transforms
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {/* Transform Type */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Transform Type</Label>
          <Select
            value={config.transformType || 'select'}
            // @ts-ignore
            onValueChange={(value) => updateConfig({ transformType: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="select">
                <div className="flex items-center gap-2">
                  <Columns className="h-3 w-3" />
                  <span className="text-xs">Select Columns</span>
                </div>
              </SelectItem>
              <SelectItem value="filter">
                <div className="flex items-center gap-2">
                  <Filter className="h-3 w-3" />
                  <span className="text-xs">Filter Rows</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Column Selection */}
        {config.transformType === 'select' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Selected Columns</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowColumnSelector(true)}
                disabled={!selectedDataset}
                className="h-7 text-xs px-2"
              >
                <Columns className="h-3 w-3 mr-1" />
                Choose
              </Button>
            </div>

            {config.selectedColumns && Array.isArray(config.selectedColumns) && config.selectedColumns.length > 0 ? (
              <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                {config.selectedColumns.map((column: string) => (
                  <Badge key={column} variant="secondary" className="text-xs">
                    {column}
                    <button
                      onClick={() => {
                        const currentColumns = config.selectedColumns || []
                        const newColumns = currentColumns.filter((c: string) => c !== column)
                        updateConfig({ selectedColumns: newColumns })
                      }}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-2 w-2" />
                    </button>
                  </Badge>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
                Click "Choose" to select columns
              </div>
            )}
          </div>
        )}

        {/* Filter Configuration */}
        {config.transformType === 'filter' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Filters</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentFilters = config.filters || []
                  const newFilters = [...currentFilters, { column: '', operator: '=', value: '' }]
                  updateConfig({ filters: newFilters })
                }}
                className="h-7 text-xs px-2"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
            </div>

            {config.filters && Array.isArray(config.filters) && config.filters.length > 0 ? (
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {config.filters.map((filter: FilterConfig, index: number) => (
                  <div key={index} className="flex items-center gap-1 p-2 border rounded text-xs">
                    <Select
                      value={filter.column || ''}
                      onValueChange={(value) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, column: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-20 h-7">
                        <SelectValue placeholder="Col" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedDataset?.headers?.map((header: string) => (
                          <SelectItem key={header} value={header}>
                            {header}
                          </SelectItem>
                        )) || []}
                      </SelectContent>
                    </Select>

                    <Select
                      value={filter.operator || '='}
                      onValueChange={(value) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, operator: value }
                        updateConfig({ filters: newFilters })
                      }}
                    >
                      <SelectTrigger className="w-16 h-7">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="=">=</SelectItem>
                        <SelectItem value="!=">!=</SelectItem>
                        <SelectItem value=">">&gt;</SelectItem>
                        <SelectItem value="<">&lt;</SelectItem>
                        <SelectItem value="contains">has</SelectItem>
                      </SelectContent>
                    </Select>

                    <Input
                      placeholder="Value"
                      value={filter.value || ''}
                      onChange={(e) => {
                        const currentFilters = config.filters || []
                        const newFilters = [...currentFilters]
                        newFilters[index] = { ...filter, value: e.target.value }
                        updateConfig({ filters: newFilters })
                      }}
                      className="flex-1 h-7 text-xs"
                    />

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const currentFilters = config.filters || []
                        const newFilters = currentFilters.filter((_: FilterConfig, i: number) => i !== index)
                        updateConfig({ filters: newFilters })
                      }}
                      className="h-7 w-7 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
                Click "Add" to create filters
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  const renderJoinConfig = () => {
    if (connectedDatasets.length < 2) {
      return (
        <div className="text-xs text-muted-foreground p-3 border border-dashed rounded text-center">
          Connect two datasets to configure join keys
          {connectedDatasets.length === 1 && (
            <div className="mt-2 text-xs">
              Connected: {connectedDatasets[0].name} (need 1 more)
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {/* Connected Datasets */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Connected Datasets</Label>
          <div className="space-y-1">
            {connectedDatasets.slice(0, 2).map((dataset, index) => (
              <div key={dataset.id} className="flex items-center gap-2 p-2 bg-muted/30 rounded text-xs">
                <DatabaseBackup className="h-3 w-3" />
                <span>{index === 0 ? 'Left' : 'Right'}: {dataset.name}</span>
                <Badge variant="outline" className="text-xs">
                  {dataset.headers.length} cols
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Join Type */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Join Type</Label>
          <Select
            value={config.joinType || 'inner'}
            // @ts-ignore
            onValueChange={(value) => updateConfig({ joinType: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="inner">
                <div className="flex items-center gap-2">
                  <GitMerge className="h-3 w-3" />
                  <span className="text-xs">Inner Join</span>
                </div>
              </SelectItem>
              <SelectItem value="left">
                <div className="flex items-center gap-2">
                  <GitMerge className="h-3 w-3" />
                  <span className="text-xs">Left Join</span>
                </div>
              </SelectItem>
              <SelectItem value="right">
                <div className="flex items-center gap-2">
                  <GitMerge className="h-3 w-3" />
                  <span className="text-xs">Right Join</span>
                </div>
              </SelectItem>
              <SelectItem value="full">
                <div className="flex items-center gap-2">
                  <GitMerge className="h-3 w-3" />
                  <span className="text-xs">Full Join</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Join Keys */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Join Keys</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newJoinKeys = [...(config.joinKeys || []), { left: '', right: '' }]
                updateConfig({ joinKeys: newJoinKeys })
              }}
              className="h-7 text-xs px-2"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Key
            </Button>
          </div>

          {config.joinKeys && config.joinKeys.length > 0 ? (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {config.joinKeys.map((joinKey: any, index: number) => (
                <div key={index} className="flex items-center gap-1 p-2 border rounded text-xs">
                  <Select
                    value={joinKey.left || ''}
                    onValueChange={(value) => {
                      const newJoinKeys = [...(config.joinKeys || [])]
                      newJoinKeys[index] = { ...joinKey, left: value }
                      updateConfig({ joinKeys: newJoinKeys })
                    }}
                  >
                    <SelectTrigger className="w-20 h-7">
                      <SelectValue placeholder="Left" />
                    </SelectTrigger>
                    <SelectContent>
                      {connectedDatasets[0]?.headers?.map((header: string) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      )) || []}
                    </SelectContent>
                  </Select>

                  <span className="text-xs text-muted-foreground">=</span>

                  <Select
                    value={joinKey.right || ''}
                    onValueChange={(value) => {
                      const newJoinKeys = [...(config.joinKeys || [])]
                      newJoinKeys[index] = { ...joinKey, right: value }
                      updateConfig({ joinKeys: newJoinKeys })
                    }}
                  >
                    <SelectTrigger className="w-20 h-7">
                      <SelectValue placeholder="Right" />
                    </SelectTrigger>
                    <SelectContent>
                      {connectedDatasets[1]?.headers?.map((header: string) => (
                        <SelectItem key={header} value={header}>
                          {header}
                        </SelectItem>
                      )) || []}
                    </SelectContent>
                  </Select>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newJoinKeys = (config.joinKeys || []).filter((_: any, i: number) => i !== index)
                      updateConfig({ joinKeys: newJoinKeys })
                    }}
                    className="h-7 w-7 p-0"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-xs text-muted-foreground p-2 border border-dashed rounded text-center">
              Click "Add Key" to specify join conditions
            </div>
          )}
        </div>
      </div>
    )
  }

  const renderConcatConfig = () => {
    if (connectedDatasets.length < 2) {
      return (
        <div className="text-xs text-muted-foreground p-3 border border-dashed rounded text-center">
          Connect multiple datasets to combine
          {connectedDatasets.length === 1 && (
            <div className="mt-2 text-xs">
              Connected: {connectedDatasets[0].name} (need 1+ more)
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="space-y-3">
        {/* Connected Datasets */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Connected Datasets</Label>
          <div className="space-y-1 max-h-24 overflow-y-auto">
            {connectedDatasets.map((dataset, index) => (
              <div key={dataset.id} className="flex items-center gap-2 p-2 bg-muted/30 rounded text-xs">
                <DatabaseBackup className="h-3 w-3" />
                <span>{dataset.name}</span>
                <Badge variant="outline" className="text-xs">
                  {dataset.data?.length || 0} rows
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {dataset.headers.length} cols
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Concat Type */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Concat Type</Label>
          <Select
            value={config.concatType || 'append'}
            // @ts-ignore
            onValueChange={(value) => updateConfig({ concatType: value })}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="append">
                <div className="flex items-center gap-2">
                  <Layers className="h-3 w-3" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Append</span>
                    <span className="text-xs text-muted-foreground">Keep all rows</span>
                  </div>
                </div>
              </SelectItem>
              <SelectItem value="union">
                <div className="flex items-center gap-2">
                  <Layers className="h-3 w-3" />
                  <div className="flex flex-col">
                    <span className="text-xs font-medium">Union</span>
                    <span className="text-xs text-muted-foreground">Remove duplicates</span>
                  </div>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Column Selection */}
        <div className="space-y-3">
          <Label className="text-xs font-medium">Column Selection</Label>

          {connectedDatasets.map((dataset, datasetIndex) => (
            <div key={dataset.id} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DatabaseBackup className="h-3 w-3" />
                  <span className="text-xs font-medium">{dataset.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Select all columns for this dataset
                      const currentSelection = config.columnSelection || {}
                      const datasetKey = dataset.name || dataset.id || `dataset_${datasetIndex}` // Use dataset name as key
                      const newSelection = {
                        ...currentSelection,
                        [datasetKey]: dataset.headers
                      }
                      updateConfig({ columnSelection: newSelection })
                    }}
                    className="h-6 px-2 text-xs"
                  >
                    All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Clear selection for this dataset
                      const currentSelection = config.columnSelection || {}
                      const datasetKey = dataset.name || dataset.id || `dataset_${datasetIndex}` // Use dataset name as key
                      const newSelection = {
                        ...currentSelection,
                        [datasetKey]: []
                      }
                      updateConfig({ columnSelection: newSelection })
                    }}
                    className="h-6 px-2 text-xs"
                  >
                    Clear
                  </Button>
                </div>
              </div>

              <div className="max-h-32 overflow-y-auto border rounded p-2 bg-muted/10">
                <div className="grid grid-cols-2 gap-1">
                  {dataset.headers.map((header: string) => {
                    const datasetKey = dataset.name || dataset.id || `dataset_${datasetIndex}` // Use dataset name as key
                    const isSelected = (config.columnSelection?.[datasetKey] || []).includes(header)
                    return (
                      <label
                        key={header}
                        className="flex items-center gap-2 p-1 hover:bg-muted/50 rounded cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            const currentSelection = config.columnSelection || {}
                            const datasetColumns = currentSelection[datasetKey] || []

                            let newColumns
                            if (e.target.checked) {
                              newColumns = [...datasetColumns, header]
                            } else {
                              newColumns = datasetColumns.filter((col: string) => col !== header)
                            }

                            const newSelection = {
                              ...currentSelection,
                              [datasetKey]: newColumns
                            }
                            updateConfig({ columnSelection: newSelection })
                          }}
                          className="w-3 h-3"
                        />
                        <span className="text-xs truncate" title={header}>
                          {header}
                        </span>
                      </label>
                    )
                  })}
                </div>
              </div>

              {/* Show selected columns count */}
              <div className="text-xs text-muted-foreground">
                Selected: {(config.columnSelection?.[dataset.name || dataset.id || `dataset_${datasetIndex}`] || []).length} / {dataset.headers.length} columns
              </div>
            </div>
          ))}

          {/* Preview of result columns */}
          {config.columnSelection && Object.keys(config.columnSelection).length > 0 && (
            <div className="mt-3 p-2 bg-primary/5 border border-primary/20 rounded">
              <div className="text-xs font-medium text-primary mb-1">Result Preview:</div>
              <div className="text-xs text-muted-foreground">
                {Object.values(config.columnSelection).flat().length} total columns will be combined
              </div>
              <div className="text-xs text-muted-foreground mt-1 max-h-16 overflow-y-auto">
                Columns: {Object.values(config.columnSelection).flat().join(', ') || 'None selected'}
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="w-full h-full max-w-4xl max-h-[90vh] flex flex-col bg-background border rounded-lg shadow-xl">
        <div className="flex-shrink-0 flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <Settings className="h-5 w-5" />
            <div>
              <h2 className="text-lg font-semibold">Configure {node.data?.type || 'Node'}</h2>
              <Badge variant="outline" className="text-xs capitalize">
                {node.data?.type || 'unknown'} node
              </Badge>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 p-4 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="max-w-2xl">
              {node.data?.type === 'transform' && renderTransformConfig()}
              {node.data?.type === 'join' && renderJoinConfig()}
              {node.data?.type === 'concat' && renderConcatConfig()}
              {node.data?.type === 'output' && (
                <div className="text-sm text-muted-foreground p-6 border border-dashed rounded text-center">
                  <Settings className="h-8 w-8 mx-auto mb-3 opacity-50" />
                  <p>Output nodes display the final pipeline result.</p>
                  <p className="text-xs mt-1">No configuration needed.</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Column Selector Modal */}
      {showColumnSelector && selectedDataset && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <ColumnSelector
            dataset={selectedDataset}
            selectedColumns={config.selectedColumns || []}
            onColumnsChange={(columns) => updateConfig({ selectedColumns: columns })}
            onClose={() => setShowColumnSelector(false)}
            onApply={() => setShowColumnSelector(false)}
          />
        </div>
      )}
    </>
  )
}
