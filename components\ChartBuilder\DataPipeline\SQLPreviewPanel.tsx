'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Code2, Co<PERSON>, Chev<PERSON>Down, ChevronUp } from "lucide-react"
import { toast } from 'sonner'

interface SQLPreviewPanelProps {
  sql: string
}

export function SQLPreviewPanel({ sql }: SQLPreviewPanelProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(sql)
      toast.success('SQL copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy SQL')
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Code2 className="h-4 w-4" />
            <CardTitle className="text-sm">SQL Preview</CardTitle>
            <Badge variant="secondary" className="text-xs">
              Auto-generated
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={copyToClipboard}
              disabled={!sql || sql.includes('Error')}
            >
              <Copy className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronUp className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {!isCollapsed && (
        <CardContent className="flex-1 pt-0">
          <div className="h-full">
            <pre className="text-xs bg-muted p-3 rounded-md h-full overflow-auto font-mono">
              <code className="text-muted-foreground">
                {sql || '-- Connect nodes to generate SQL preview'}
              </code>
            </pre>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
