'use client'

import { useState, useMemo, useRef, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { BarChart3, AlertCircle, TableIcon, ImageIcon, LineChart, PieChart, AreaChart, Maximize2, PlusCircle } from "lucide-react"
import { NotesEditor } from './NotesEditor'
import { ChartVisualizer } from './ChartVisualizer'
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { GraphicWalkerVisualization } from './GraphicWalker'
import { toast } from "sonner"
import { RichDataTable } from './RichDataTable'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Default chart colors array
const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

// Streamlit app data interface
interface StreamlitAppData {
  type: 'streamlit_app';
  embed_url: string;
  open_url: string;
  title: string;
}

// Media type definitions
type MediaType = 'image' | 'video' | 'streamlit_app' | 'iframe';

interface QueryResultProps {
  data?: any[]
  error?: string
  output?: string
  plots?: string[]
  result?: any  // Add result prop for Jupyter-like return value display
  isSuccess?: boolean
  showGraphicWalker?: boolean
  onSaveChart?: (data: any[], config: any, chartType: 'line' | 'bar' | 'pie' | 'area', chartId?: string) => void
  onSaveTable?: (data: any[], columns: string[], tableId?: string) => void
  onSavePlot?: (plotUrl: string, plotTitle: string, plotId?: string) => void
  errorDetails?: {
    message: string;
    code?: string;
    stack?: string;
    serverTrace?: string;
  };
  executionTime?: number; // in milliseconds
  chartType?: 'line' | 'bar' | 'pie' | 'area'
  onChartTypeChange?: (type: 'line' | 'bar' | 'pie' | 'area') => void
  viewMode?: 'table' | 'chart' | 'output' | 'graphicwalker'
  onViewModeChange?: (mode: 'table' | 'chart' | 'output' | 'graphicwalker') => void
  cellId?: string // Add cellId prop to identify this specific result
  language?: string; // Add language prop to identify the cell language

  // Cell control props
  onMoveUp?: (id: string) => void
  onMoveDown?: (id: string) => void
  notes?: string
  onUpdateNotes?: (id: string, notes: string) => void
}

// Keep track of chart configurations in memory using this simple cache
const chartConfigCache: Record<string, any> = {};

export function QueryResult({
  data,
  error,
  output,
  plots,
  result,
  isSuccess,
  showGraphicWalker = false,
  onSaveChart,
  onSaveTable,
  onSavePlot,
  errorDetails,
  executionTime,
  chartType: propChartType = 'bar',
  onChartTypeChange,
  viewMode: propViewMode = 'table',
  onViewModeChange,
  cellId = 'default',
  language = 'sql',
  // Cell control props
  onMoveUp,
  onMoveDown,
  notes = '[]',
  onUpdateNotes
}: QueryResultProps) {
  // Track rendered state to prevent unnecessary updates
  const hasRenderedChart = useRef(false);

  // State for tracking iframe loading
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>({});

  // Use the props as initial values, but check cache first
  const [viewMode, setViewMode] = useState<'table' | 'chart' | 'output' | 'plots' | 'graphicwalker'>(propViewMode);
  const [chartType, setChartType] = useState<'line' | 'bar' | 'pie' | 'area'>(() => {
    return chartConfigCache[cellId]?.type || propChartType;
  });

  const [chartConfig, setChartConfig] = useState<any>(() => {
    return chartConfigCache[cellId] || null;
  });

  // Simple cache-based handler for chart config updates
  const handleChartConfigUpdate = (newConfig: any) => {
    setChartConfig(newConfig);
    // Store in memory cache instead of URL
    chartConfigCache[cellId] = newConfig;
  };

  // Handle chart type change and update cache
  const handleChartTypeChange = (type: 'line' | 'bar' | 'pie' | 'area') => {
    setChartType(type);

    // Also update the config in cache
    const updatedConfig = { ...chartConfig, type };
    chartConfigCache[cellId] = updatedConfig;
    setChartConfig(updatedConfig);

    if (onChartTypeChange) {
      onChartTypeChange(type);
    }
  };

  // Process data for charting
  const processedData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    try {
      return data.map((row, index) => {
        if (!row || typeof row !== 'object') {
          return { _index: index };
        }

        const newRow = { ...row, _index: index };

        // Only convert boolean values to numbers for charting
        Object.keys(row).forEach(key => {
          if (typeof row[key] === 'boolean') {
            newRow[`${key}_value`] = row[key] ? 1 : 0;
          }
        });

        return newRow;
      });
    } catch (error) {
      console.error('Error processing data for chart:', error);
      return [];
    }
  }, [data]);

  // Find suitable numeric columns
  const numericColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return ['_index'];

    const firstRow = processedData[0];
    const numericCols = Object.keys(firstRow).filter(key => {
      const value = firstRow[key];
      // Check if value is already a number or can be converted to one
      return typeof value === 'number' ||
        (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '');
    });

    if (!numericCols.includes('_index')) {
      numericCols.push('_index');
    }

    return numericCols;
  }, [processedData]);

  // Find suitable category columns
  const categoryColumns = useMemo(() => {
    if (!processedData || processedData.length === 0) return [];

    const firstRow = processedData[0];
    return Object.keys(firstRow).filter(key =>
      typeof firstRow[key] === 'string' &&
      processedData.length < 100 // Limit categories for performance
    );
  }, [processedData]);

  // Get columns for chart configuration
  const columns = useMemo(() =>
    processedData.length > 0 ? Object.keys(processedData[0]) : [],
    [processedData]
  );

  // Simple chart config with default values
  const defaultChartConfig = useMemo(() => ({
    xAxis: categoryColumns[0] || columns[0] || '_index',
    yAxis: numericColumns[0] || '_index',
    title: 'Data Visualization',
    description: 'Chart visualization',
    showLegend: true,
    showLabels: false,
    showGrid: true,
  }), [categoryColumns, numericColumns, columns]);

  // Update the parent when viewMode changes
  const handleViewModeChange = (mode: 'table' | 'chart' | 'output' | 'plots' | 'graphicwalker') => {
    if (viewMode !== mode) {
      setViewMode(mode);
      if (onViewModeChange) {
        onViewModeChange(mode);
      }

      // Reset chart rendering flag when switching away from chart
      if (mode !== 'chart') {
        hasRenderedChart.current = false;
      }
    }
  };

  // Check if data is available
  const hasData = useMemo(() =>
    data && data.length > 0,
    [data]
  );

  // Format cell value for display
  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) return '-'
    if (typeof value === 'object') return JSON.stringify(value)
    if (typeof value === 'number') return value.toLocaleString()

    // Check if it's a base64 image and show a placeholder instead
    if (typeof value === 'string') {
      if (value.startsWith('data:image') ||
          (value.includes('base64,')) ||
          (/^[A-Za-z0-9+/=]+$/.test(value.substring(0, 20)) && value.length > 100)) {
        return '[Image Data]'
      }
    }

    return String(value)
  }

  // We no longer automatically store results in the dashboard store
  // This prevents duplicate items when saving to dashboard
  // Users must explicitly click "Save to Dashboard" button

  // Handle saving chart to dashboard
  const handleSaveChart = () => {
    if (onSaveChart && processedData.length > 0) {
      // Get the current complete chart configuration
      const currentConfig = chartConfig || defaultChartConfig;

      // Create a complete config object with all properties
      const completeConfig = {
        // Base properties
        xAxis: currentConfig.xAxis || defaultChartConfig.xAxis,
        yAxis: currentConfig.yAxis || defaultChartConfig.yAxis,
        title: currentConfig.title || defaultChartConfig.title,
        description: currentConfig.description || defaultChartConfig.description,

        // Visual properties
        showLegend: currentConfig.showLegend !== undefined ? currentConfig.showLegend : defaultChartConfig.showLegend,
        showLabels: currentConfig.showLabels !== undefined ? currentConfig.showLabels : defaultChartConfig.showLabels,
        showGrid: currentConfig.showGrid !== undefined ? currentConfig.showGrid : defaultChartConfig.showGrid,
        type: chartType,
        color: currentConfig.color || COLORS[0],

        // Advanced properties - preserve all configuration
        aggregation: currentConfig.aggregation,
        groupBy: currentConfig.groupBy,
        timeScale: currentConfig.timeScale,
        customLabel: currentConfig.customLabel,
        enableZoom: currentConfig.enableZoom !== undefined ? currentConfig.enableZoom : true,
        multiSeries: currentConfig.multiSeries,
        fontSize: currentConfig.fontSize,

        // Preserve any other properties that might be in the config
        ...currentConfig
      };

      // Create a chart object with a MongoDB-compatible ID
      const chartId = Date.now().toString(16).padStart(24, '0');

      // Create the saved chart with complete configuration
      const savedChart = {
        id: chartId,
        type: 'chart' as const,
        title: completeConfig.title || 'Untitled Chart',
        description: completeConfig.description || 'Chart visualization',
        chartType: chartType,
        data: processedData,
        config: completeConfig,
        gridColumn: 0,
        gridRow: 0,
        width: 6,
        height: 4,
        createdAt: new Date()
      };

      // Call the parent callback which will add the chart to the Zustand store
      // This prevents duplicate charts from being created
      onSaveChart(processedData, completeConfig, chartType, chartId);

      toast.success("Chart saved to dashboard");
    } else {
      if (!processedData.length) {
        toast.error("No data available to save");
      } else if (!onSaveChart) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving table to dashboard
  const handleSaveTable = () => {
    if (onSaveTable && data && data.length > 0) {
      // Create a table object with a unique ID
      const tableId = `table-${cellId}-${Date.now()}`;

      // Get columns from the first row of data
      const columns = data.length > 0 ? Object.keys(data[0]) : [];

      // Call the parent callback to save the table
      onSaveTable(data, columns, tableId);

      toast.success("Table saved to dashboard");
    } else {
      if (!data || data.length === 0) {
        toast.error("No table data available to save");
      } else if (!onSaveTable) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle saving plot to dashboard
  const handleSavePlot = (plotUrl: string, plotTitle: string) => {
    if (onSavePlot && plotUrl) {
      // Create a plot object with a unique ID
      const plotId = `plot-${cellId}-${Date.now()}`;

      // Call the parent callback to save the plot
      onSavePlot(plotUrl, plotTitle, plotId);

      toast.success(`${plotTitle} saved to dashboard`);
    } else {
      if (!plotUrl) {
        toast.error("Invalid plot data");
      } else if (!onSavePlot) {
        toast.error("Save functionality not available");
      }
    }
  };

  // Handle cleanup of Streamlit apps
  const handleCleanupStreamlitApp = async (appId: string) => {
    try {
      const response = await fetch('/api/cleanup-streamlit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ app_id: appId }),
      });

      if (response.ok) {
        toast.success('Streamlit app stopped successfully');
      } else {
        toast.error('Failed to stop Streamlit app');
      }
    } catch (error) {
      console.error('Error stopping Streamlit app:', error);
      toast.error('Error stopping Streamlit app');
    }
  };

  // Render empty state if no data
  if (!hasData && !output && !plots?.length && !error) {
    return null;
  }

  return (
    <Card className={cn(
      "w-full overflow-hidden group relative shadow-none border-l-2 border-l-muted border-t-0 border-r-0 border-b-0 rounded-none",
      error ? "border-l-red-500" : isSuccess ? "border-l-green-500" : "border-l-muted"
    )}>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between px-2 py-1 border-b bg-muted/30 gap-1">
        <span className="text-xs text-muted-foreground">
          {data && data.length > 0 ? `${data.length} rows` : ''}
          {output ? ' | Has output' : ''}
          {plots && plots.length > 0 ? ` | ${plots.length} plot${plots.length > 1 ? 's' : ''}` : ''}
          {executionTime ? ` | ${(executionTime / 1000).toFixed(2)}s` : ''}
        </span>

        <div className="flex flex-wrap gap-1">
          {error && (
            <span className="text-sm text-red-500 font-medium">
              Execution Failed
            </span>
          )}
          {isSuccess && (
            <span className="text-sm text-green-500 font-medium">
              Execution Successful
            </span>
          )}



          {/* Table button with Notes icon next to it */}
          <div className="flex items-center gap-1">
            {hasData && (
              <Button
                variant={viewMode === 'table' ? 'default' : 'outline'}
                size="sm"
                className="h-6 px-1.5 text-[10px]"
                onClick={() => handleViewModeChange('table')}
              >
                <TableIcon className="h-3 w-3 mr-1" />
                Table
              </Button>
            )}

            {/* Notes Button */}
            {onUpdateNotes && (
              <div className="h-6 flex items-center">
                <NotesEditor
                  notes={notes}
                  onSave={(updatedNotes) => onUpdateNotes(cellId, updatedNotes)}
                />
              </div>
            )}
          </div>

          {/* Chart button */}
          {hasData && (
            <Button
              variant={viewMode === 'chart' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('chart')}
              data-chart-button
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Chart
            </Button>
          )}

          {/* Visual Explorer button */}
          {hasData && (
            <Button
              variant={viewMode === 'graphicwalker' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => handleViewModeChange('graphicwalker')}
            >
              <Maximize2 className="h-3 w-3 mr-1" />
              Visual Explorer
            </Button>
          )}

          {/* Unified Output/Plots View button */}
          {(output || (plots && plots.length > 0) || (language === 'python')) && (
            <Button
              variant={viewMode === 'output' ? 'default' : 'outline'}
              size="sm"
              className="h-6 px-1.5 text-[10px]"
              onClick={() => {
                // Log plots data for debugging
                console.log("Output data:", output);
                console.log("Plots data:", plots);
                handleViewModeChange('output');
              }}
            >
              <ImageIcon className="h-3 w-3 mr-1" />
              Output {plots?.length ? `& Plots (${plots.length})` : ''}
            </Button>
          )}
        </div>
      </div>

      {/* Chart Type Selector - Only visible when chart view is active */}
      {viewMode === 'chart' && (
        <div className="flex flex-wrap items-center gap-1 p-0.5 border-b bg-muted/20">
          {/* <div className="flex items-center gap-1">
            <Button
              variant={chartType === 'bar' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('bar')}
            >
              <BarChart3 className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'line' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('line')}
            >
              <LineChart className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'area' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('area')}
            >
              <AreaChart className="h-3 w-3" />
            </Button>

            <Button
              variant={chartType === 'pie' ? 'default' : 'outline'}
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => handleChartTypeChange('pie')}
            >
              <PieChart className="h-3 w-3" />
            </Button>
          </div> */}

          {/* Add Save to Dashboard button */}
          {onSaveChart && (
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-1.5 ml-auto text-[10px]"
              onClick={handleSaveChart}
            >
              Save to Dashboard
            </Button>
          )}
        </div>
      )}

      {/* Content Area */}
      <div className="relative">
        {/* Error View */}
        {error && (
          <div className="p-1">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="ml-2">
                <div className="font-medium">{error}</div>
                {errorDetails && (
                  <div className="mt-2 space-y-2">
                    {errorDetails.code && (
                      <div className="text-xs">
                        <span className="font-semibold">Error code:</span> {errorDetails.code}
                      </div>
                    )}
                    {errorDetails.serverTrace && (
                      <div className="overflow-auto max-h-[150px] text-xs font-mono whitespace-pre-wrap p-1 bg-background/40 rounded-md border">
                        {errorDetails.serverTrace}
                      </div>
                    )}
                    {executionTime && (
                      <div className="text-xs mt-1">
                        Execution time: {(executionTime / 1000).toFixed(2)}s
                      </div>
                    )}
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Compact Split View Layout using grid */}
        <div className="grid gap-1 px-2 py-1 grid-cols-1">
          {/* Charts Section - make more compact */}
          {viewMode === 'chart' && hasData && (
            <div className="col-span-full border rounded-md overflow-hidden">
              <div className="h-full">
                <ChartVisualizer
                  key={`chart-${cellId}`}
                  data={processedData}
                  initialChartType={chartType}
                  chartConfig={chartConfig || defaultChartConfig}
                  showConfig={true}
                  onConfigChange={handleChartConfigUpdate}
                  cellId={cellId}
                />
              </div>
            </div>
          )}

          {/* Table View */}
          {viewMode === 'table' && hasData && (
            <RichDataTable
              data={data || []}
              onSaveTable={onSaveTable}
              onSaveToTable={handleSaveTable}
              maxHeight="350px"
            />
          )}

          {/* GraphicWalker View - full width */}
          {viewMode === 'graphicwalker' && hasData && (
            <div className="col-span-full p-0 border rounded-md">
              <GraphicWalkerVisualization
                data={data || []}
                title="Interactive Data Explorer"
                onBack={() => handleViewModeChange('table')}
              />
            </div>
          )}

          {/* Unified Output & Plots View */}
          {viewMode === 'output' && (output || error || (plots && plots.length > 0)) && (
            <div className="max-h-[500px] overflow-y-auto border rounded-md">
              <div className="p-2 space-y-3">
                {/* Text Output Section - Always show for Jupyter-like experience */}
                <div>
                  <div className="text-xs text-muted-foreground mb-1 font-medium flex items-center gap-1">
                    <span>📄 Console Output:</span>
                    {language === 'python' && (
                      <span className="text-[10px] bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 rounded">
                        Python
                      </span>
                    )}
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button className="text-muted-foreground hover:text-foreground transition-colors">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="max-w-xs">
                          <div className="text-xs space-y-1">
                            <div className="font-medium">Jupyter-like Features:</div>
                            <div>• Variables persist between cells</div>
                            <div>• Use <code>print()</code> for console output</div>
                            <div>• Set <code>result = value</code> for return values</div>
                            <div>• <code>print(locals())</code> shows all variables</div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <pre className={cn(
                    "whitespace-pre-wrap p-3 rounded-md text-sm font-mono min-h-[60px]",
                    error ? "bg-red-50 dark:bg-red-950/20 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800" :
                    output ? "bg-muted border" : "bg-gray-50 dark:bg-gray-900 border border-dashed text-muted-foreground"
                  )}>
                    {error ? `❌ Error:\n${error}` :
                     output ? output :
                     "No console output (code executed silently)"}
                  </pre>

                  {/* Result/Return Value Section - Jupyter-like */}
                  {result && typeof result === 'object' && (
                    <div className="mt-2">
                      <div className="text-xs text-muted-foreground mb-1 font-medium flex items-center gap-1">
                        <span>📤 Result:</span>
                        <span className="text-[10px] bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 px-1 rounded">
                          Object
                        </span>
                      </div>
                      <div className="bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800 rounded-md p-3">
                        <pre className="text-sm font-mono text-purple-800 dark:text-purple-200 whitespace-pre-wrap">
                          {JSON.stringify(result, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Simple Result Value */}
                  {result && typeof result !== 'object' && (
                    <div className="mt-2">
                      <div className="text-xs text-muted-foreground mb-1 font-medium flex items-center gap-1">
                        <span>📤 Result:</span>
                        <span className="text-[10px] bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-1 rounded">
                          {typeof result}
                        </span>
                      </div>
                      <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                        <pre className="text-sm font-mono text-green-800 dark:text-green-200">
                          {String(result)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Embedded Streamlit App in Output Section */}
                  {result && typeof result === 'object' && result.type === 'streamlit_app_info' && (() => {
                    // Initialize loading state for output Streamlit app
                    if (loadingStates['output'] === undefined) {
                      setLoadingStates(prev => ({ ...prev, 'output': true }));
                    }
                    return true;
                  })() && (
                    <div className="mt-3">
                      <div className="text-xs text-muted-foreground mb-2 font-medium flex items-center gap-1">
                        <span>🎈 Interactive Streamlit App:</span>
                        <span className="text-[10px] bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-1 rounded">
                          Live
                        </span>
                      </div>
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                        {/* App Header */}
                        <div className="flex justify-between items-center mb-3">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                              🎈 Live Streamlit Application
                            </span>
                            <span className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                              ● Running
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs h-6 bg-white dark:bg-gray-800"
                              onClick={() => window.open(result.url, '_blank')}
                            >
                              🔗 Open in New Tab
                            </Button>
                            {result.app_id && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6 text-red-600 hover:text-red-700 bg-white dark:bg-gray-800"
                                onClick={() => handleCleanupStreamlitApp(result.app_id)}
                              >
                                🛑 Stop
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Embedded App */}
                        <div className="relative bg-white dark:bg-gray-900 rounded-md border border-blue-200 dark:border-blue-700 overflow-hidden">
                          <div className="bg-blue-100 dark:bg-blue-900 px-3 py-1 text-xs text-blue-800 dark:text-blue-200 border-b border-blue-200 dark:border-blue-700">
                            📱 Interactive App - You can interact with widgets directly below
                          </div>
                          <iframe
                            src={result.embed_url}
                            className="w-full border-0"
                            style={{ height: '500px' }}
                            title="Embedded Streamlit App"
                            allow="camera; microphone; geolocation; clipboard-read; clipboard-write"
                            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-modals"
                            onLoad={() => {
                              console.log('Streamlit app embedded in output successfully');
                              setLoadingStates(prev => ({ ...prev, 'output': false }));
                              toast.success('Streamlit app loaded in output!');
                            }}
                            onError={() => {
                              console.warn('Failed to embed Streamlit app in output');
                              setLoadingStates(prev => ({ ...prev, 'output': false }));
                              toast.error('Failed to embed Streamlit app');
                            }}
                          />

                          {/* Loading overlay for output section */}
                          {(loadingStates['output'] !== false) && (
                            <div className="absolute inset-0 bg-white/90 dark:bg-gray-900/90 flex items-center justify-center transition-opacity duration-500 pointer-events-none">
                              <div className="text-center space-y-3">
                                <div className="animate-spin w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
                                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">Loading Streamlit app...</div>
                                <div className="text-xs text-muted-foreground">Initializing interactive environment</div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* App Info */}
                        <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                          💡 This is your live Streamlit app embedded directly in the output. You can interact with all widgets and features here.
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Plots & Apps Section */}
                {plots && plots.length > 0 && (
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-xs text-muted-foreground font-medium flex items-center gap-1">
                        <span>🎨 Visualizations & Apps ({plots.length}):</span>
                        <span className="text-[10px] bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-1 rounded">
                          Interactive
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button className="text-muted-foreground hover:text-foreground transition-colors">
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                </svg>
                              </button>
                            </TooltipTrigger>
                            <TooltipContent side="top" className="max-w-xs">
                              <div className="text-xs space-y-1">
                                <div className="font-medium">Streamlit Apps:</div>
                                <div>• Write Streamlit code to create live apps</div>
                                <div>• Use <code>import streamlit as st</code></div>
                                <div>• Apps run automatically and embed here</div>
                                <div>• Click "Open in New Tab" for full view</div>
                                <div>• Use <code>get_random_streamlit_app()</code> for demos</div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex gap-1">
                        {onSavePlot && (
                          <div className="text-xs text-muted-foreground">
                            💾 Save to Dashboard
                          </div>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-6"
                          onClick={() => {
                            console.log('Raw plots data:', plots);
                            if (plots && plots.length > 0) {
                              const plotPreview = plots.map((p, i) => ({
                                index: i,
                                preview: p.substring(0, 50) + '...',
                                isDataUrl: p.startsWith('data:'),
                                isUrl: p.startsWith('http'),
                                isStreamlit: p.includes('streamlit_app'),
                                length: p.length
                              }));
                              console.table(plotPreview);
                              toast.success(`Debug info logged. Found ${plots.length} items.`);
                            } else {
                              toast.error('No visualization data available');
                            }
                          }}
                        >
                          🔍 Debug
                        </Button>
                      </div>
                    </div>

                    {/* Render plots */}
                    <div className="space-y-2">
                      {plots.map((plot, index) => {
                        // Enhanced media detection and handling for images, videos, and Streamlit apps
                        let mediaData: string = '';
                        let mediaType: MediaType = 'image'; // default to image
                        let mimeType: string = '';
                        let streamlitAppData: StreamlitAppData | null = null;

                        try {
                          // Debug: Log the plot data to understand what we're getting
                          console.log(`Plot ${index + 1} data preview:`, plot.substring(0, 100) + '...');
                          console.log(`Plot ${index + 1} starts with {?`, plot.startsWith('{'));
                          console.log(`Plot ${index + 1} includes streamlit_app?`, plot.includes('"type":"streamlit_app"'));

                          // Check if it's a Streamlit app object (JSON string)
                          if (plot.startsWith('{') && plot.includes('"type":"streamlit_app"')) {
                            try {
                              streamlitAppData = JSON.parse(plot);
                              mediaType = 'streamlit_app';
                              mediaData = streamlitAppData?.embed_url || '';
                              console.log(`✅ Successfully parsed Streamlit app data for plot ${index + 1}:`, streamlitAppData);
                            } catch (e) {
                              console.warn('Failed to parse Streamlit app data:', e);
                              return null;
                            }
                          }
                          // Check if it's already a complete data URL
                          else if (plot.startsWith('data:')) {
                            mediaData = plot;
                            // Extract media type from data URL
                            const mimeMatch = plot.match(/data:([^;]+)/);
                            if (mimeMatch) {
                              mimeType = mimeMatch[1];
                              if (mimeType.startsWith('image/')) {
                                mediaType = 'image';
                              } else if (mimeType.startsWith('video/')) {
                                mediaType = 'video';
                              }
                            }
                          } else if (plot.startsWith('http')) {
                            // Handle URLs - could be Streamlit apps, images, or other content
                            if (plot.includes('streamlit') || plot.includes('8501') || plot.includes('.streamlit.app')) {
                              mediaType = 'streamlit_app';
                              // Create streamlit app data for legacy URLs
                              const embedUrl = plot.includes('?embed=true') ? plot : `${plot}?embed=true&embed_options=hide_loading_screen`;
                              const openUrl = plot.split('?')[0];
                              streamlitAppData = {
                                type: 'streamlit_app' as const,
                                embed_url: embedUrl,
                                open_url: openUrl,
                                title: 'Streamlit App'
                              };
                              mediaData = embedUrl;
                            } else if (plot.match(/\.(jpg|jpeg|png|gif|webp|svg)(\?|$)/i)) {
                              mediaType = 'image';
                              mediaData = plot;
                            } else {
                              // Generic URL - treat as iframe
                              mediaType = 'iframe';
                              mediaData = plot;
                            }
                          } else {
                            // Try to parse as base64 image
                            try {
                              mediaData = `data:image/png;base64,${plot}`;
                              mediaType = 'image';
                            } catch {
                              console.warn('Could not parse plot data:', plot.substring(0, 100));
                              return null;
                            }
                          }

                          // Initialize loading state for Streamlit apps
                          if (mediaType === 'streamlit_app' && loadingStates[index] === undefined) {
                            setLoadingStates(prev => ({ ...prev, [index]: true }));
                          }

                          return (
                            <div key={index} className="border rounded-md p-2 bg-background">
                              <div className="flex justify-between items-center mb-2">
                                <span className="text-xs text-muted-foreground">
                                  {mediaType === 'streamlit_app' ? `🎈 Streamlit App ${index + 1}` : `Plot ${index + 1} (${mediaType})`}
                                </span>
                                {onSavePlot && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-xs h-6"
                                    onClick={() => {
                                      if (mediaType === 'streamlit_app' && streamlitAppData) {
                                        // Save Streamlit app with proper data
                                        handleSavePlot(JSON.stringify(streamlitAppData), `Streamlit App ${index + 1}`);
                                      } else {
                                        handleSavePlot(plot, `Plot ${index + 1}`);
                                      }
                                    }}
                                  >
                                    Save to Dashboard
                                  </Button>
                                )}
                              </div>

                              {/* Render based on media type */}
                              {mediaType === 'image' && (
                                <img
                                  src={mediaData}
                                  alt={`Plot ${index + 1}`}
                                  className="max-w-full h-auto rounded border"
                                  style={{ maxHeight: '400px' }}
                                  onError={(e) => {
                                    console.error('Image load error:', e);
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                              )}

                              {mediaType === 'video' && (
                                <video
                                  src={mediaData}
                                  controls
                                  className="max-w-full h-auto rounded border"
                                  style={{ maxHeight: '400px' }}
                                >
                                  Your browser does not support the video tag.
                                </video>
                              )}

                              {mediaType === 'streamlit_app' && streamlitAppData && (
                                <div className="space-y-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                                  {/* Header with app info */}
                                  <div className="flex justify-between items-start">
                                    <div className="space-y-1">
                                      <div className="flex items-center gap-2">
                                        <span className="text-lg">🎈</span>
                                        <span className="font-medium text-blue-700 dark:text-blue-300">
                                          {streamlitAppData.title}
                                        </span>
                                        {(streamlitAppData as any).app_id && (
                                          <span className="text-xs bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 px-2 py-1 rounded-full font-medium">
                                            ● Live App
                                          </span>
                                        )}
                                        <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                                          Interactive
                                        </span>
                                      </div>
                                      <div className="text-sm text-muted-foreground">
                                        <span className="font-mono text-xs bg-background px-2 py-1 rounded border">
                                          {streamlitAppData.open_url.replace('https://', '').replace('http://', '')}
                                        </span>
                                      </div>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-xs h-7 bg-white dark:bg-gray-800"
                                        onClick={() => streamlitAppData && window.open(streamlitAppData.open_url, '_blank')}
                                      >
                                        🔗 Open in New Tab
                                      </Button>
                                      {(streamlitAppData as any).app_id && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="text-xs h-7 text-red-600 hover:text-red-700 bg-white dark:bg-gray-800"
                                          onClick={() => handleCleanupStreamlitApp((streamlitAppData as any).app_id)}
                                        >
                                          🛑 Stop App
                                        </Button>
                                      )}
                                    </div>
                                  </div>

                                  {/* Status indicator for localhost apps */}
                                  {streamlitAppData.embed_url.includes('localhost') && (
                                    <div className="text-xs text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-900/30 p-2 rounded border border-amber-200 dark:border-amber-800">
                                      <div className="flex items-center gap-1">
                                        <span>⚡</span>
                                        <span className="font-medium">Local Development Mode</span>
                                      </div>
                                      <div className="mt-1">
                                        App is running locally. If you see connection issues, make sure the Streamlit server is still running.
                                      </div>
                                    </div>
                                  )}

                                  {/* Embedded Streamlit App */}
                                  <div className="relative bg-white dark:bg-gray-900 rounded-lg border-2 border-blue-200 dark:border-blue-800 overflow-hidden">
                                    <div className="bg-blue-100 dark:bg-blue-900 px-3 py-2 text-xs font-medium text-blue-800 dark:text-blue-200 border-b border-blue-200 dark:border-blue-700">
                                      📱 Live Streamlit Application
                                    </div>
                                    <iframe
                                      src={streamlitAppData.embed_url}
                                      className="w-full border-0"
                                      style={{ height: '600px' }}
                                      title={`Streamlit App ${index + 1}`}
                                      allow="camera; microphone; geolocation; clipboard-read; clipboard-write"
                                      sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-downloads allow-modals"
                                      onError={() => {
                                        console.warn('Streamlit iframe failed to load:', streamlitAppData?.embed_url);
                                        setLoadingStates(prev => ({ ...prev, [index]: false }));
                                        toast.error('Failed to load Streamlit app. The app might be unavailable.');
                                      }}
                                      onLoad={() => {
                                        console.log('Streamlit app loaded successfully:', streamlitAppData?.open_url);
                                        setLoadingStates(prev => ({ ...prev, [index]: false }));
                                        toast.success('Streamlit app loaded successfully!');
                                      }}
                                    />

                                    {/* Loading overlay */}
                                    {(loadingStates[index] !== false) && (
                                      <div className="absolute inset-0 bg-white/90 dark:bg-gray-900/90 flex items-center justify-center transition-opacity duration-500 pointer-events-none">
                                        <div className="text-center space-y-3">
                                          <div className="animate-spin w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
                                          <div className="text-sm font-medium text-blue-700 dark:text-blue-300">Loading Streamlit app...</div>
                                          <div className="text-xs text-muted-foreground">Setting up interactive environment</div>
                                        </div>
                                      </div>
                                    )}
                                  </div>

                                  {/* App actions */}
                                  <div className="flex justify-between items-center pt-2 border-t border-blue-200 dark:border-blue-800">
                                    <div className="text-xs text-muted-foreground">
                                      💡 This is a live, interactive Streamlit application running your code
                                    </div>
                                    {onSavePlot && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-xs h-6 bg-white dark:bg-gray-800"
                                        onClick={() => {
                                          handleSavePlot(JSON.stringify(streamlitAppData), `Streamlit App ${index + 1}`);
                                        }}
                                      >
                                        💾 Save to Dashboard
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              )}

                              {mediaType === 'iframe' && (
                                <div className="space-y-2">
                                  <div className="text-sm text-muted-foreground">
                                    URL:
                                    <a
                                      href={mediaData}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="ml-1 text-blue-500 hover:underline"
                                    >
                                      {mediaData}
                                    </a>
                                  </div>
                                  <iframe
                                    src={mediaData}
                                    className="w-full rounded border"
                                    style={{ height: '400px' }}
                                    title={`Content ${index + 1}`}
                                  />
                                </div>
                              )}
                            </div>
                          );
                        } catch (error) {
                          console.error('Error rendering plot:', error);
                          return (
                            <div key={index} className="border rounded-md p-2 bg-red-50 dark:bg-red-950/20">
                              <div className="text-sm text-red-600 dark:text-red-400">
                                Error rendering plot {index + 1}: {error instanceof Error ? error.message : 'Unknown error'}
                              </div>
                            </div>
                          );
                        }
                      })}
                    </div>


                  </div>
                )}
              </div>
            </div>
          )}



        </div>
      </div>
    </Card>
  );
}

